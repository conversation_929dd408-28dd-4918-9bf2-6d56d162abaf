#!/bin/bash
# 📊 МОНИТОРИНГ ПОПЫТОК ПОКУПКИ
# Автор: Pump.fun Trading Bot
# Дата: 2025-07-25

echo "📊 МОНИТОРИНГ ПОПЫТОК ПОКУПКИ ТОКЕНОВ"
echo "====================================="
echo "🧪 ТЕСТОВЫЙ РЕЖИМ: Отслеживание решений о покупке"
echo "⏰ Время запуска: $(date)"
echo ""
echo "🔍 Отслеживаемые события:"
echo "- INSTANT BUY (немедленная покупка)"
echo "- QUARANTINE INSTANT BUY (покупка из карантина)"
echo "- buy_error (ошибки покупки)"
echo "- STABLE GROWTH (подтверждение стабильного роста)"
echo ""
echo "Нажмите Ctrl+C для остановки мониторинга"
echo "========================================="
echo ""

# Мониторинг основного лога с фильтрацией важных событий
tail -f logs/bot-sniper-2_*.log | grep --line-buffered -E "(INSTANT BUY|QUARANTINE.*BUY|buy_error|STABLE GROWTH|PRICE CRASH|GROWTH CHECK)" | while read line; do
    timestamp=$(echo "$line" | cut -d' ' -f1-2)
    event=$(echo "$line" | grep -oE "(INSTANT BUY|QUARANTINE.*BUY|buy_error|STABLE GROWTH|PRICE CRASH|GROWTH CHECK)")
    
    case "$event" in
        *"INSTANT BUY"*)
            echo "🎯 [$timestamp] $line"
            ;;
        *"buy_error"*)
            echo "❌ [$timestamp] $line"
            ;;
        *"STABLE GROWTH"*)
            echo "📈 [$timestamp] $line"
            ;;
        *"PRICE CRASH"*)
            echo "🚨 [$timestamp] $line"
            ;;
        *"GROWTH CHECK"*)
            echo "🔍 [$timestamp] $line"
            ;;
        *)
            echo "📊 [$timestamp] $line"
            ;;
    esac
done
