#!/usr/bin/env python3
"""
Скрипт для проверки баланса токена Chewy в кошельке
"""

import asyncio
import os
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient
from spl.token.instructions import get_associated_token_address

load_dotenv()

async def check_token_balance():
    """Проверяет баланс токена Chewy в кошельке"""
    
    # Токен Chewy
    token_mint = "Hy3cHuW6fQLk1J8uEBKd15SpkYPTZpgTi2ies4Bwpump"
    
    # Получаем приватный ключ из .env
    private_key_str = os.getenv("SOLANA_PRIVATE_KEY")
    if not private_key_str:
        print("❌ SOLANA_PRIVATE_KEY не найден в .env файле!")
        return
    
    # Создаем кошелек
    try:
        private_key_bytes = base58.b58decode(private_key_str)
        keypair = Keypair.from_bytes(private_key_bytes)
        wallet_address = str(keypair.pubkey())
        print(f"🔑 Адрес кошелька: {wallet_address}")
        print(f"🪙 Токен Chewy: {token_mint}")
    except Exception as e:
        print(f"❌ Ошибка создания кошелька: {e}")
        return
    
    # Подключаемся к RPC
    rpc_endpoint = os.getenv("SOLANA_NODE_RPC_ENDPOINT", "https://api.mainnet-beta.solana.com")
    print(f"🌐 RPC endpoint: {rpc_endpoint}")
    
    try:
        async with AsyncClient(rpc_endpoint) as client:
            # Получаем associated token account
            mint_pubkey = Pubkey.from_string(token_mint)
            ata = get_associated_token_address(keypair.pubkey(), mint_pubkey)
            print(f"📍 Associated Token Account: {ata}")
            
            # Проверяем баланс токена
            try:
                token_account_info = await client.get_token_account_balance(ata)
                if token_account_info.value:
                    balance = token_account_info.value.amount
                    decimals = token_account_info.value.decimals
                    balance_decimal = int(balance) / (10 ** decimals)
                    
                    print(f"💰 Баланс Chewy: {balance_decimal:,.6f} токенов")
                    print(f"   Raw amount: {balance}")
                    print(f"   Decimals: {decimals}")
                    
                    if balance_decimal > 0:
                        print("🚨 ТОКЕН ВСЕ ЕЩЕ В КОШЕЛЬКЕ! СТОП-ЛОСС НЕ ПРОДАЛ!")
                        print("   Нужно продать токен вручную или исправить ошибку")
                    else:
                        print("✅ Токен продан, баланс = 0")
                        
                else:
                    print("❌ Не удалось получить информацию о токене")
                    
            except Exception as e:
                if "could not find account" in str(e).lower():
                    print("✅ Associated Token Account не найден - токен не покупался или уже продан")
                else:
                    print(f"❌ Ошибка получения баланса токена: {e}")
                
    except Exception as e:
        print(f"❌ Ошибка подключения к RPC: {e}")

if __name__ == "__main__":
    asyncio.run(check_token_balance())
