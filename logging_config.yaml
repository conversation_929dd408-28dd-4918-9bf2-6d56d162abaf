# Конфигурация логирования для pump.fun bot
# Настройки для предотвращения проблем с VS Code при больших лог-файлах

logging:
  # Основные настройки
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  
  # Настройки ротации файлов
  rotation:
    # Максимальный размер файла перед ротацией (в байтах)
    # 200KB ≈ 1000 строк (оптимально для VS Code)
    max_bytes: 200000

    # Количество backup файлов для обычных логов
    backup_count: 10

    # Количество backup файлов для audit логов
    audit_backup_count: 10
  
  # Настройки для разных типов логов
  handlers:
    # Основные логи ботов
    bot_logs:
      max_bytes: 200000      # 200KB (~1000 строк)
      backup_count: 10
      level: INFO

    # Audit логи (торговые операции)
    audit_logs:
      max_bytes: 200000      # 200KB (~1000 строк)
      backup_count: 10       # Уменьшено количество backup файлов
      level: INFO

    # Логи ошибок
    error_logs:
      max_bytes: 100000      # 100KB (~500 строк)
      backup_count: 10
      level: ERROR

    # Debug логи (если включены)
    debug_logs:
      max_bytes: 200000      # 200KB (~1000 строк)
      backup_count: 10
      level: DEBUG

  # Автоматическая очистка
  auto_cleanup:
    enabled: true

    # Максимальное количество строк в активном файле
    # При превышении файл будет автоматически ротирован
    max_lines_per_file: 1000

    # Проверять размер файлов каждые N минут
    check_interval_minutes: 30

    # Автоматически архивировать старые файлы
    auto_archive_old_files: true

    # Возраст файлов для архивирования (в днях)
    archive_after_days: 7

    # Удалять архивы старше N дней
    delete_archives_after_days: 30

# Специальные настройки для VS Code
vscode_optimization:
  # Максимальный размер файла, который VS Code может комфортно открыть
  max_file_size_for_vscode: 200000  # 200KB (~1000 строк)

  # Предупреждать при превышении размера
  warn_on_large_files: true

  # Автоматически создавать .gitignore для больших логов
  auto_gitignore_large_logs: true

# Мониторинг логов
monitoring:
  # Отслеживать рост размера файлов
  track_file_growth: true
  
  # Уведомлять при быстром росте логов
  alert_on_rapid_growth: true
  
  # Порог для "быстрого роста" (строк в минуту)
  rapid_growth_threshold: 100
  
  # Статистика использования логов
  collect_stats: true
