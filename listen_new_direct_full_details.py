#!/usr/bin/env python3
"""
Chainstack-style listener for pump.fun CREATE tokens using only logsSubscribe.
Computes all data on the fly without additional RPC calls.
"""

import asyncio
import websockets
import json
import base64
import struct

# Constants
PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
CREATE_DISCRIMINATOR = 8530921459188068891

def parse_create_instruction(program_data: bytes) -> dict:
    """Parse CREATE instruction data to extract token details."""
    try:
        # Skip discriminator (8 bytes)
        data = program_data[8:]

        # Parse name (32 bytes, null-terminated)
        name_bytes = data[0:32]
        name = name_bytes.rstrip(b'\x00').decode('utf-8', errors='ignore')

        # Parse symbol (16 bytes, null-terminated)
        symbol_bytes = data[32:48]
        symbol = symbol_bytes.rstrip(b'\x00').decode('utf-8', errors='ignore')

        # Parse URI (200 bytes, null-terminated)
        uri_bytes = data[48:248]
        uri = uri_bytes.rstrip(b'\x00').decode('utf-8', errors='ignore')

        # Parse mint (32 bytes) - convert to base58
        mint_bytes = data[248:280]
        mint = base64.b58encode(mint_bytes).decode('ascii')

        # Parse bonding curve (32 bytes) - convert to base58
        bonding_curve_bytes = data[280:312]
        bonding_curve = base64.b58encode(bonding_curve_bytes).decode('ascii')

        # Parse user (32 bytes) - convert to base58
        user_bytes = data[312:344]
        user = base64.b58encode(user_bytes).decode('ascii')

        return {
            'name': name,
            'symbol': symbol,
            'uri': uri,
            'mint': mint,
            'bonding_curve': bonding_curve,
            'user': user
        }

    except Exception as e:
        print(f"Error parsing CREATE instruction: {e}")
        return None

async def listen_pump_fun_creates():
    """Listen for pump.fun CREATE events using only logsSubscribe."""
    
    uri = 'wss://solana-mainnet.core.chainstack.com/3906e15d8166b689d7f687b3925098e2'
    
    try:
        async with websockets.connect(uri) as websocket:
            # Subscribe to logs mentioning pump.fun program
            subscribe_msg = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'logsSubscribe',
                'params': [
                    {'mentions': [PUMP_FUN_PROGRAM_ID]},
                    {'commitment': 'processed'}
                ]
            }
            await websocket.send(json.dumps(subscribe_msg))
            
            print('🎯 Listening for pump.fun CREATE tokens...')
            print('📡 Using only logsSubscribe - no additional RPC calls needed!')
            print('=' * 80)
            
            # Confirm subscription
            response = await websocket.recv()
            data = json.loads(response)
            if 'result' in data:
                print(f'✅ Subscribed with ID: {data["result"]}')
            
            token_count = 0
            
            # Listen for logs
            while True:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(response)
                    
                    if 'params' in data:
                        logs = data['params']['result']['value']['logs']
                        signature = data['params']['result']['value']['signature']
                        
                        # Check if this is a CREATE instruction
                        has_create = any("Program log: Instruction: Create" in log for log in logs)
                        
                        if has_create:
                            # Find Program data lines
                            for log_line in logs:
                                if 'Program data:' in log_line:
                                    try:
                                        # Extract and decode program data
                                        encoded_data = log_line.split(': ')[1]
                                        program_data = base64.b64decode(encoded_data)
                                        
                                        # Check discriminator
                                        if len(program_data) >= 8:
                                            discriminator = struct.unpack('<Q', program_data[:8])[0]
                                            
                                            if discriminator == CREATE_DISCRIMINATOR:
                                                # Parse CREATE instruction
                                                token_info = parse_create_instruction(program_data)
                                                
                                                if token_info:
                                                    token_count += 1
                                                    
                                                    print(f'\n🎉 NEW TOKEN #{token_count} DETECTED!')
                                                    print(f'📝 Signature: {signature}')
                                                    print(f'🏷️  Name: {token_info["name"]} ({token_info["symbol"]})')
                                                    print(f'🪙 Mint: {token_info["mint"]}')
                                                    print(f'👤 Creator: {token_info["user"]}')
                                                    print(f'📈 Bonding Curve: {token_info["bonding_curve"]}')
                                                    print(f'🌐 URI: {token_info["uri"][:50]}...')
                                                    print('=' * 80)
                                                    
                                                break
                                                
                                    except Exception as e:
                                        print(f'⚠️ Error processing program data: {e}')
                        
                except asyncio.TimeoutError:
                    print('⏳ No new logs in 10 seconds...')
                    continue
                except Exception as e:
                    print(f'❌ Error processing message: {e}')
                    continue
                        
    except Exception as e:
        print(f'❌ Connection error: {e}')

if __name__ == "__main__":
    print('🚀 Starting pump.fun CREATE token listener...')
    print('📡 Method: logsSubscribe only (Chainstack style)')
    print('🎯 Target: CREATE instructions with full details')
    print()
    
    asyncio.run(listen_pump_fun_creates())
