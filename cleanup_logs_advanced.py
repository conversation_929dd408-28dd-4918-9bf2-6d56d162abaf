#!/usr/bin/env python3
"""
Продвинутый скрипт очистки больших лог-файлов pump.fun бота
Предотвращает зависание VS Code при работе с большими файлами
"""

import os
import sys
import gzip
import shutil
from datetime import datetime
from pathlib import Path
import argparse

# Цвета для вывода
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def log(message, color=Colors.BLUE):
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"{color}[{timestamp}]{Colors.NC} {message}")

def error(message):
    log(message, Colors.RED)

def success(message):
    log(message, Colors.GREEN)

def warning(message):
    log(message, Colors.YELLOW)

def format_size(size_bytes):
    """Форматирование размера файла"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.1f}GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.1f}MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.1f}KB"
    else:
        return f"{size_bytes}B"

def get_file_info(file_path):
    """Получение информации о файле"""
    try:
        stat = file_path.stat()
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = sum(1 for _ in f)
        return {
            'size': stat.st_size,
            'lines': lines,
            'modified': datetime.fromtimestamp(stat.st_mtime)
        }
    except Exception as e:
        error(f"Ошибка получения информации о файле {file_path}: {e}")
        return None

def archive_log_file(log_file, keep_lines=1000):
    """Архивирование лог-файла с сохранением последних строк"""
    try:
        log_path = Path(log_file)
        if not log_path.exists():
            error(f"Файл не найден: {log_file}")
            return False
        
        info = get_file_info(log_path)
        if not info:
            return False
        
        log(f"Обработка файла: {log_path.name}")
        log(f"  Размер: {format_size(info['size'])}")
        log(f"  Строк: {info['lines']}")
        
        if info['lines'] <= keep_lines:
            warning(f"  Файл содержит {info['lines']} строк, меньше лимита {keep_lines}. Пропускаем.")
            return True
        
        # Создание директории для архивов
        archive_dir = Path("logs/archived")
        archive_dir.mkdir(exist_ok=True)
        
        # Чтение всех строк
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            all_lines = f.readlines()
        
        # Разделение на архивную и текущую части
        archive_lines = all_lines[:-keep_lines]
        current_lines = all_lines[-keep_lines:]
        
        # Создание архивного файла
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        archive_name = f"{log_path.stem}_{timestamp}.log.gz"
        archive_path = archive_dir / archive_name
        
        # Сохранение архива
        with gzip.open(archive_path, 'wt', encoding='utf-8') as f:
            f.writelines(archive_lines)
        
        # Перезапись оригинального файла
        with open(log_path, 'w', encoding='utf-8') as f:
            f.writelines(current_lines)
        
        # Проверка результата
        new_info = get_file_info(log_path)
        archive_info = archive_path.stat()
        
        success(f"  Архивировано: {len(archive_lines)} строк → {archive_name}")
        success(f"  Оставлено: {len(current_lines)} строк")
        success(f"  Новый размер: {format_size(new_info['size'])}")
        
        return True
        
    except Exception as e:
        error(f"Ошибка архивирования файла {log_file}: {e}")
        return False

def find_problematic_logs(logs_dir="logs", line_limit=5000, size_limit_mb=10):
    """Поиск проблемных лог-файлов (большие по размеру или количеству строк)"""
    logs_path = Path(logs_dir)
    if not logs_path.exists():
        error(f"Директория логов не найдена: {logs_dir}")
        return []
    
    problematic_files = []
    size_limit_bytes = size_limit_mb * 1024 * 1024
    
    for log_file in logs_path.glob("*.log"):
        try:
            info = get_file_info(log_file)
            if info and (info['size'] > size_limit_bytes or info['lines'] > line_limit):
                problematic_files.append((log_file, info))
        except Exception as e:
            warning(f"Не удалось проверить файл {log_file}: {e}")
    
    return sorted(problematic_files, key=lambda x: x[1]['lines'], reverse=True)

def main():
    parser = argparse.ArgumentParser(description="Продвинутая очистка лог-файлов")
    parser.add_argument("--line-limit", type=int, default=5000, 
                       help="Лимит строк для VS Code (по умолчанию: 5000)")
    parser.add_argument("--size-limit", type=int, default=10, 
                       help="Лимит размера файла в MB (по умолчанию: 10)")
    parser.add_argument("--keep-lines", type=int, default=1000,
                       help="Количество строк для сохранения (по умолчанию: 1000)")
    parser.add_argument("--logs-dir", default="logs",
                       help="Директория с логами (по умолчанию: logs)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Только показать файлы без обработки")
    parser.add_argument("--force", action="store_true",
                       help="Обработать все файлы без подтверждения")
    
    args = parser.parse_args()
    
    print(f"\n{Colors.CYAN}=== ОЧИСТКА ПРОБЛЕМНЫХ ЛОГ-ФАЙЛОВ ==={Colors.NC}\n")
    print(f"Критерии проблемных файлов:")
    print(f"  • Больше {args.line_limit} строк (проблемы с VS Code)")
    print(f"  • Больше {args.size_limit}MB размером")
    print()
    
    # Поиск проблемных файлов
    log(f"Поиск проблемных файлов в директории {args.logs_dir}...")
    problematic_files = find_problematic_logs(args.logs_dir, args.line_limit, args.size_limit)
    
    if not problematic_files:
        success("Проблемных лог-файлов не найдено!")
        return
    
    print(f"\nНайдено {len(problematic_files)} проблемных файлов:")
    total_size = 0
    total_lines = 0
    
    for log_file, info in problematic_files:
        status = []
        if info['lines'] > args.line_limit:
            status.append(f">{args.line_limit} строк")
        if info['size'] > args.size_limit * 1024 * 1024:
            status.append(f">{args.size_limit}MB")
        
        print(f"  {log_file.name}: {format_size(info['size'])} ({info['lines']} строк) - {', '.join(status)}")
        total_size += info['size']
        total_lines += info['lines']
    
    print(f"\nОбщая статистика:")
    print(f"  Размер: {format_size(total_size)}")
    print(f"  Строк: {total_lines}")
    
    if args.dry_run:
        log("Режим dry-run: файлы не будут изменены")
        return
    
    # Подтверждение
    if not args.force:
        print(f"\nПараметры обработки:")
        print(f"  Сохранить последних строк: {args.keep_lines}")
        print(f"  Архивировать остальные строки в logs/archived/")
        
        try:
            response = input(f"\nПродолжить обработку {len(problematic_files)} файлов? (y/N): ")
            if response.lower() not in ['y', 'yes', 'да']:
                log("Операция отменена")
                return
        except KeyboardInterrupt:
            print("\nОперация отменена")
            return
    
    # Обработка файлов
    print(f"\n{Colors.CYAN}=== ОБРАБОТКА ФАЙЛОВ ==={Colors.NC}\n")
    
    processed = 0
    errors = 0
    
    for log_file, info in problematic_files:
        if archive_log_file(log_file, args.keep_lines):
            processed += 1
        else:
            errors += 1
        print()
    
    # Итоги
    print(f"{Colors.CYAN}=== ИТОГИ ==={Colors.NC}")
    success(f"Обработано файлов: {processed}")
    if errors > 0:
        error(f"Ошибок: {errors}")
    
    # Проверка результата
    remaining_problematic = find_problematic_logs(args.logs_dir, args.line_limit, args.size_limit)
    if remaining_problematic:
        warning(f"Остались проблемные файлы: {len(remaining_problematic)}")
    else:
        success("Все проблемные файлы обработаны! VS Code больше не должен зависать.")

if __name__ == "__main__":
    main()
