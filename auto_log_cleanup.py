#!/usr/bin/env python3
"""
Автоматическая очистка логов при старте бота
Обеспечивает соблюдение лимитов: 1000 строк на файл, 10 файлов максимум
"""

import os
import gzip
import shutil
from pathlib import Path
from datetime import datetime

def count_lines(file_path):
    """Подсчет строк в файле."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0

def archive_large_file(file_path, archive_dir):
    """Архивирование большого файла."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    archive_name = f"{file_path.stem}_{timestamp}.gz"
    archive_path = archive_dir / archive_name
    
    with open(file_path, 'rb') as f_in:
        with gzip.open(archive_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    
    file_path.unlink()
    return archive_path

def cleanup_old_files(directory, pattern, max_files=10):
    """Удаление старых файлов, оставляя только max_files последних."""
    files = list(directory.glob(pattern))
    if len(files) <= max_files:
        return 0
    
    # Сортируем по времени модификации (новые первыми)
    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    # Удаляем старые файлы
    deleted_count = 0
    for old_file in files[max_files:]:
        old_file.unlink()
        deleted_count += 1
    
    return deleted_count

def auto_cleanup_logs(silent=True):
    """
    Автоматическая очистка логов.
    
    Args:
        silent: Если True, выводит минимум информации
    """
    logs_dir = Path("logs")
    if not logs_dir.exists():
        return
    
    archive_dir = logs_dir / "archived"
    archive_dir.mkdir(exist_ok=True)
    
    archived_count = 0
    deleted_count = 0
    
    # 1. Архивируем файлы >1000 строк
    log_files = list(logs_dir.glob("*.log*"))
    for log_file in log_files:
        if log_file.is_file():
            lines = count_lines(log_file)
            if lines > 1000:
                archive_large_file(log_file, archive_dir)
                archived_count += 1
    
    # 2. Удаляем старые файлы (оставляем только 10)
    deleted_count += cleanup_old_files(logs_dir, "bot-sniper-*.log*", 10)
    deleted_count += cleanup_old_files(logs_dir, "audit.log*", 10)
    deleted_count += cleanup_old_files(logs_dir, "purchases_*.txt*", 10)
    deleted_count += cleanup_old_files(archive_dir, "*.gz", 10)
    
    if not silent:
        print(f"🧹 Автоочистка логов: архивировано {archived_count}, удалено {deleted_count}")
    
    return archived_count, deleted_count

if __name__ == "__main__":
    print("🧹 АВТОМАТИЧЕСКАЯ ОЧИСТКА ЛОГОВ")
    print("=" * 40)
    archived, deleted = auto_cleanup_logs(silent=False)
    print(f"✅ Завершено: архивировано {archived}, удалено {deleted}")
