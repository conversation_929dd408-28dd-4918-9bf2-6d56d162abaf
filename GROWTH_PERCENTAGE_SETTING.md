# 🎯 НОВАЯ НАСТРОЙКА: min_growth_percentage

## 🔍 ОБНАРУЖЕННАЯ ПРОБЛЕМА

Пользователь спросил: **"на какой процент настроен min_growth_checks: 3?"**

**ПРОБЛЕМА:** Оказалось что `min_growth_checks` проверял **ЛЮБОЙ** рост, даже 0.001%!

## ❌ СТАРАЯ ЛОГИКА (ПРОБЛЕМНАЯ):

```python
# Старый код в _check_stable_growth()
if current_price > prev_price:  # ← ЛЮБОЙ рост!
    self.growth_checks[mint] += 1
```

**Что происходило:**
```
Проверка 1: 1.000 → 1.001 (+0.1%) ✅ ЗАСЧИТАНО
Проверка 2: 1.001 → 1.002 (+0.1%) ✅ ЗАСЧИТАНО  
Проверка 3: 1.002 → 1.003 (+0.1%) ✅ ЗАСЧИТАНО
Результат: 3/3 → ПОКУПКА (при росте всего 0.3%!)
```

**ПРОБЛЕМА:** Бот покупал токены при **МИКРОСКОПИЧЕСКОМ** росте!

## ✅ НОВАЯ ЛОГИКА (ИСПРАВЛЕННАЯ):

### 1. Добавлена настройка в YAML:

```yaml
filters:
  min_growth_checks: 3               # Количество проверок роста
  min_growth_percentage: 0.02        # Минимум 2% роста для каждой проверки
  recheck_delay: 1.1                 # Интервал между проверками
```

### 2. Исправлен код проверки роста:

```python
# Новый код в _check_stable_growth()
growth_percentage = (current_price - prev_price) / prev_price
min_growth_percentage = self.filters_cfg.get("min_growth_percentage", 0.01)

if growth_percentage >= min_growth_percentage:
    self.growth_checks[mint] += 1
    logger.info(f"🛡️ GROWTH CHECK: {token_info.symbol} рост {growth_percentage*100:.2f}% >= {min_growth_percentage*100:.1f}% ✅")
else:
    self.growth_checks[mint] = 0  # Сброс счетчика!
    logger.info(f"🛡️ GROWTH CHECK: {token_info.symbol} рост {growth_percentage*100:.2f}% < {min_growth_percentage*100:.1f}% ❌")
```

## 🎯 НОВОЕ ПОВЕДЕНИЕ:

**При настройке `min_growth_percentage: 0.02` (2%):**

```
Проверка 1: 1.000 → 1.001 (+0.1% < 2%) ❌ СБРОС СЧЕТЧИКА
Проверка 2: 1.001 → 1.025 (+2.4% ≥ 2%) ✅ ЗАСЧИТАНО (1/3)
Проверка 3: 1.025 → 1.046 (+2.0% ≥ 2%) ✅ ЗАСЧИТАНО (2/3)  
Проверка 4: 1.046 → 1.067 (+2.0% ≥ 2%) ✅ ЗАСЧИТАНО (3/3)
Результат: 3/3 → ПОКУПКА (при реальном росте 6%+)
```

## 🛠️ НАСТРОЙКИ В YAML:

### Основные параметры:

```yaml
filters:
  # 🛡️ НАСТРОЙКИ ПРОВЕРКИ РОСТА
  min_growth_checks: 3               # Сколько проверок роста подряд
  min_growth_percentage: 0.02        # Минимальный процент роста (2%)
  
  # ⏱️ НАСТРОЙКИ ВРЕМЕНИ
  recheck_delay: 1.1                 # Интервал между проверками (сек)
  min_stability_period: 15           # Период анализа истории (сек)
  
  # 📊 НАСТРОЙКИ ТРЕНДА
  trend_tolerance: 0.07              # Толерантность к падениям (7%)
```

### Рекомендуемые значения:

| Настройка | Описание | Эффект |
|-----------|----------|--------|
| `0.005` (0.5%) | Очень мягкая | Почти любой рост засчитывается |
| `0.01` (1%) | Мягкая | Минимальный фильтр роста |
| `0.02` (2%) | **РЕКОМЕНДУЕТСЯ** | Умеренная защита от шума |
| `0.03` (3%) | Строгая | Только значительный рост |
| `0.05` (5%) | Очень строгая | Только сильный рост |

## 🎯 ВЛИЯНИЕ НА ТОРГОВЛЮ:

### ❌ Старое поведение:
- Покупка при любом микроросте
- Много ложных сигналов
- Покупка "шумовых" движений

### ✅ Новое поведение:
- Покупка только при значимом росте
- Фильтрация рыночного шума
- Более качественные сигналы

## 🚀 ОЖИДАЕМЫЙ РЕЗУЛЬТАТ:

1. **Меньше ложных покупок** - фильтрация микроколебаний
2. **Лучшее качество сигналов** - только реальный рост
3. **Настраиваемая чувствительность** - можно адаптировать под рынок
4. **Защита от шума** - игнорирование незначительных движений

## 📁 ИЗМЕНЕННЫЕ ФАЙЛЫ:

- `bots/bot-sniper-2-logs.yaml`: Добавлен параметр `min_growth_percentage: 0.02`
- `src/trading/trader.py`: Обновлена логика `_check_stable_growth()`
- `src/trading/buyer.py`: Добавлен параметр в конструктор

## 💡 РЕКОМЕНДАЦИИ ПО ИСПОЛЬЗОВАНИЮ:

**Для начала:** `min_growth_percentage: 0.02` (2%)
**Если много ложных сигналов:** увеличить до 0.03 (3%)
**Если мало покупок:** уменьшить до 0.01 (1%)

**Теперь у вас есть полный контроль над чувствительностью бота к росту цен!** 🎯
