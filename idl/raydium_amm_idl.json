{"version": "0.3.0", "name": "raydium_amm", "instructions": [{"name": "initialize", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "coinMintAddress", "isMut": false, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": false, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolTargetOrdersAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": false, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "userWallet", "isMut": true, "isSigner": true}], "args": [{"name": "nonce", "type": "u8"}, {"name": "openTime", "type": "u64"}]}, {"name": "initialize2", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "splAssociatedTokenAccount", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "lpMint", "isMut": true, "isSigner": false}, {"name": "coinMint", "isMut": false, "isSigner": false}, {"name": "pcMint", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolTempLp", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "userWallet", "isMut": true, "isSigner": true}, {"name": "userTokenCoin", "isMut": true, "isSigner": false}, {"name": "userTokenPc", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}], "args": [{"name": "nonce", "type": "u8"}, {"name": "openTime", "type": "u64"}, {"name": "initPcAmount", "type": "u64"}, {"name": "initCoinAmount", "type": "u64"}]}, {"name": "monitorStep", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "serumReqQ", "isMut": true, "isSigner": false}, {"name": "serumEventQ", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}], "args": [{"name": "planOrderLimit", "type": "u16"}, {"name": "placeOrderLimit", "type": "u16"}, {"name": "cancelOrderLimit", "type": "u16"}]}, {"name": "deposit", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": false, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "userCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "userPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "userOwner", "isMut": false, "isSigner": true}, {"name": "serumEventQueue", "isMut": false, "isSigner": false}], "args": [{"name": "maxCoinAmount", "type": "u64"}, {"name": "maxPcAmount", "type": "u64"}, {"name": "baseSide", "type": "u64"}]}, {"name": "withdraw", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "uer<PERSON><PERSON><PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "userOwner", "isMut": false, "isSigner": true}, {"name": "serumEventQ", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "migrateToOpenBook", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTokenCoin", "isMut": true, "isSigner": false}, {"name": "ammTokenPc", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "newAmmOpenOrders", "isMut": true, "isSigner": false}, {"name": "newSerumProgram", "isMut": false, "isSigner": false}, {"name": "newSerumMarket", "isMut": false, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}], "args": []}, {"name": "setParams", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "ammC<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "ammPcVault", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "ammAdminAccount", "isMut": false, "isSigner": true}], "args": [{"name": "param", "type": "u8"}, {"name": "value", "type": {"option": "u64"}}, {"name": "new<PERSON><PERSON><PERSON>", "type": {"option": "public<PERSON>ey"}}, {"name": "fees", "type": {"option": {"defined": "Fees"}}}, {"name": "lastOrderDistance", "type": {"option": {"defined": "LastOrderDistance"}}}, {"name": "needTakeAmounts", "type": {"option": {"defined": "NeedTake"}}}]}, {"name": "withdrawPnl", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammConfig", "isMut": false, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinPnlTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcPnlTokenAccount", "isMut": true, "isSigner": false}, {"name": "pnlOwnerAccount", "isMut": false, "isSigner": true}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}], "args": []}, {"name": "withdrawSrm", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": false, "isSigner": false}, {"name": "ammOwnerAccount", "isMut": false, "isSigner": true}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "srmToken", "isMut": true, "isSigner": false}, {"name": "destSrmToken", "isMut": true, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "swapBaseIn", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "uerSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "uerDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "userSourceOwner", "isMut": false, "isSigner": true}], "args": [{"name": "amountIn", "type": "u64"}, {"name": "minimumAmountOut", "type": "u64"}]}, {"name": "preInitialize", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "coinMintAddress", "isMut": false, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "userWallet", "isMut": true, "isSigner": true}], "args": [{"name": "nonce", "type": "u8"}]}, {"name": "swapBaseOut", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "uerSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "uerDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "userSourceOwner", "isMut": false, "isSigner": true}], "args": [{"name": "maxAmountIn", "type": "u64"}, {"name": "amountOut", "type": "u64"}]}, {"name": "simulateInfo", "accounts": [{"name": "amm", "isMut": false, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": false, "isSigner": false}, {"name": "lpMintAddress", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "serumEventQueue", "isMut": false, "isSigner": false}], "args": [{"name": "param", "type": "u8"}, {"name": "swapBaseInValue", "type": {"option": {"defined": "SwapInstructionBaseIn"}}}, {"name": "swapBaseOutValue", "type": {"option": {"defined": "SwapInstructionBaseOut"}}}]}, {"name": "adminCancelOrders", "accounts": [{"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "amm", "isMut": false, "isSigner": false}, {"name": "ammAuthority", "isMut": false, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammTargetOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "ammOwnerAccount", "isMut": false, "isSigner": true}, {"name": "ammConfig", "isMut": true, "isSigner": false}, {"name": "serumProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "serumEventQ", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}], "args": [{"name": "limit", "type": "u16"}]}, {"name": "createConfigAccount", "accounts": [{"name": "admin", "isMut": true, "isSigner": true}, {"name": "ammConfig", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateConfigAccount", "accounts": [{"name": "admin", "isMut": false, "isSigner": true}, {"name": "ammConfig", "isMut": true, "isSigner": false}], "args": [{"name": "param", "type": "u8"}, {"name": "owner", "type": "public<PERSON>ey"}]}], "accounts": [{"name": "TargetOrders", "type": {"kind": "struct", "fields": [{"name": "owner", "type": {"array": ["u64", 4]}}, {"name": "buyOrders", "type": {"array": [{"defined": "TargetOrder"}, 50]}}, {"name": "padding1", "type": {"array": ["u64", 8]}}, {"name": "targetX", "type": "u128"}, {"name": "targetY", "type": "u128"}, {"name": "planXBuy", "type": "u128"}, {"name": "planYBuy", "type": "u128"}, {"name": "planXSell", "type": "u128"}, {"name": "planYSell", "type": "u128"}, {"name": "placedX", "type": "u128"}, {"name": "placedY", "type": "u128"}, {"name": "calcPnlX", "type": "u128"}, {"name": "calcPnlY", "type": "u128"}, {"name": "sellOrders", "type": {"array": [{"defined": "TargetOrder"}, 50]}}, {"name": "padding2", "type": {"array": ["u64", 6]}}, {"name": "replaceBuyClientId", "type": {"array": ["u64", 10]}}, {"name": "replaceSellClientId", "type": {"array": ["u64", 10]}}, {"name": "lastOrderNumerator", "type": "u64"}, {"name": "lastOrderDenominator", "type": "u64"}, {"name": "planOrdersCur", "type": "u64"}, {"name": "placeOrdersCur", "type": "u64"}, {"name": "validBuyOrderNum", "type": "u64"}, {"name": "validSellOrderNum", "type": "u64"}, {"name": "padding3", "type": {"array": ["u64", 10]}}, {"name": "freeSlotBits", "type": "u128"}]}}, {"name": "Fees", "type": {"kind": "struct", "fields": [{"name": "minSeparateNumerator", "type": "u64"}, {"name": "minSeparateDenominator", "type": "u64"}, {"name": "tradeFeeNumerator", "type": "u64"}, {"name": "tradeFeeDenominator", "type": "u64"}, {"name": "pnlNumerator", "type": "u64"}, {"name": "pnlDenominator", "type": "u64"}, {"name": "swapFeeNumerator", "type": "u64"}, {"name": "swapFeeDenominator", "type": "u64"}]}}, {"name": "AmmInfo", "type": {"kind": "struct", "fields": [{"name": "status", "type": "u64"}, {"name": "nonce", "type": "u64"}, {"name": "orderNum", "type": "u64"}, {"name": "depth", "type": "u64"}, {"name": "coinDecimals", "type": "u64"}, {"name": "pcDecimals", "type": "u64"}, {"name": "state", "type": "u64"}, {"name": "resetFlag", "type": "u64"}, {"name": "minSize", "type": "u64"}, {"name": "volMaxCutRatio", "type": "u64"}, {"name": "amountWave", "type": "u64"}, {"name": "coinLotSize", "type": "u64"}, {"name": "pcLotSize", "type": "u64"}, {"name": "minPriceMultiplier", "type": "u64"}, {"name": "maxPriceMultiplier", "type": "u64"}, {"name": "sysDecimalValue", "type": "u64"}, {"name": "fees", "type": {"defined": "Fees"}}, {"name": "outPut", "type": {"defined": "OutPutData"}}, {"name": "tokenCoin", "type": "public<PERSON>ey"}, {"name": "tokenPc", "type": "public<PERSON>ey"}, {"name": "coinMint", "type": "public<PERSON>ey"}, {"name": "pcMint", "type": "public<PERSON>ey"}, {"name": "lpMint", "type": "public<PERSON>ey"}, {"name": "openOrders", "type": "public<PERSON>ey"}, {"name": "market", "type": "public<PERSON>ey"}, {"name": "serumDex", "type": "public<PERSON>ey"}, {"name": "targetOrders", "type": "public<PERSON>ey"}, {"name": "withdrawQueue", "type": "public<PERSON>ey"}, {"name": "tokenTempLp", "type": "public<PERSON>ey"}, {"name": "ammOwner", "type": "public<PERSON>ey"}, {"name": "lpAmount", "type": "u64"}, {"name": "clientOrderId", "type": "u64"}, {"name": "padding", "type": {"array": ["u64", 2]}}]}}], "types": [{"name": "WithdrawDestToken", "type": {"kind": "struct", "fields": [{"name": "withdrawAmount", "type": "u64"}, {"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "destTokenCoin", "type": "public<PERSON>ey"}, {"name": "destTokenPc", "type": "public<PERSON>ey"}]}}, {"name": "Withdraw<PERSON><PERSON>ue", "type": {"kind": "struct", "fields": [{"name": "owner", "type": {"array": ["u64", 4]}}, {"name": "head", "type": "u64"}, {"name": "count", "type": "u64"}, {"name": "buf", "type": {"array": [{"defined": "WithdrawDestToken"}, 64]}}]}}, {"name": "TargetOrder", "type": {"kind": "struct", "fields": [{"name": "price", "type": "u64"}, {"name": "vol", "type": "u64"}]}}, {"name": "OutPutData", "type": {"kind": "struct", "fields": [{"name": "needTakePnlCoin", "type": "u64"}, {"name": "needTakePnlPc", "type": "u64"}, {"name": "totalPnlPc", "type": "u64"}, {"name": "totalPnlCoin", "type": "u64"}, {"name": "poolOpenTime", "type": "u64"}, {"name": "punishPcAmount", "type": "u64"}, {"name": "punishCoinAmount", "type": "u64"}, {"name": "orderbookToInitTime", "type": "u64"}, {"name": "swapCoinInAmount", "type": "u128"}, {"name": "swapPcOutAmount", "type": "u128"}, {"name": "swapTakePcFee", "type": "u64"}, {"name": "swapPcInAmount", "type": "u128"}, {"name": "swapCoinOutAmount", "type": "u128"}, {"name": "swapTakeCoinFee", "type": "u64"}]}}, {"name": "AmmConfig", "type": {"kind": "struct", "fields": [{"name": "pnlOwner", "type": "public<PERSON>ey"}, {"name": "cancelOwner", "type": "public<PERSON>ey"}, {"name": "pending1", "type": {"array": ["u64", 28]}}, {"name": "pending2", "type": {"array": ["u64", 32]}}]}}, {"name": "LastOrderDistance", "type": {"kind": "struct", "fields": [{"name": "lastOrderNumerator", "type": "u64"}, {"name": "lastOrderDenominator", "type": "u64"}]}}, {"name": "NeedTake", "type": {"kind": "struct", "fields": [{"name": "needTakePc", "type": "u64"}, {"name": "needTakeCoin", "type": "u64"}]}}, {"name": "SwapInstructionBaseIn", "type": {"kind": "struct", "fields": [{"name": "amountIn", "type": "u64"}, {"name": "minimumAmountOut", "type": "u64"}]}}, {"name": "SwapInstructionBaseOut", "type": {"kind": "struct", "fields": [{"name": "maxAmountIn", "type": "u64"}, {"name": "amountOut", "type": "u64"}]}}], "errors": [{"code": 0, "name": "AlreadyInUse", "msg": "AlreadyInUse"}, {"code": 1, "name": "InvalidProgramAddress", "msg": "InvalidProgramAddress"}, {"code": 2, "name": "ExpectedMint", "msg": "ExpectedMint"}, {"code": 3, "name": "ExpectedAccount", "msg": "ExpectedAccount"}, {"code": 4, "name": "InvalidCoin<PERSON>ault", "msg": "InvalidCoin<PERSON>ault"}, {"code": 5, "name": "InvalidPCVault", "msg": "InvalidPCVault"}, {"code": 6, "name": "InvalidTokenLP", "msg": "InvalidTokenLP"}, {"code": 7, "name": "InvalidDestTokenCoin", "msg": "InvalidDestTokenCoin"}, {"code": 8, "name": "InvalidDestTokenPC", "msg": "InvalidDestTokenPC"}, {"code": 9, "name": "InvalidPoolMint", "msg": "InvalidPoolMint"}, {"code": 10, "name": "InvalidOpenOrders", "msg": "InvalidOpenOrders"}, {"code": 11, "name": "InvalidSerumMarket", "msg": "InvalidSerumMarket"}, {"code": 12, "name": "InvalidSerumProgram", "msg": "InvalidSerumProgram"}, {"code": 13, "name": "InvalidTargetOrders", "msg": "InvalidTargetOrders"}, {"code": 14, "name": "InvalidWithdrawQueue", "msg": "InvalidWithdrawQueue"}, {"code": 15, "name": "InvalidTempLp", "msg": "InvalidTempLp"}, {"code": 16, "name": "InvalidCoinMint", "msg": "InvalidCoinMint"}, {"code": 17, "name": "InvalidPCMint", "msg": "InvalidPCMint"}, {"code": 18, "name": "InvalidOwner", "msg": "InvalidOwner"}, {"code": 19, "name": "InvalidSupply", "msg": "InvalidSupply"}, {"code": 20, "name": "InvalidDelegate", "msg": "InvalidDelegate"}, {"code": 21, "name": "InvalidSignAccount", "msg": "Invalid Sign Account"}, {"code": 22, "name": "InvalidStatus", "msg": "InvalidStatus"}, {"code": 23, "name": "InvalidInstruction", "msg": "Invalid instruction"}, {"code": 24, "name": "WrongAccountsNumber", "msg": "Wrong accounts number"}, {"code": 25, "name": "WithdrawTransferBusy", "msg": "Withdraw_transfer is busy"}, {"code": 26, "name": "WithdrawQueueFull", "msg": "WithdrawQueue is full"}, {"code": 27, "name": "WithdrawQueueEmpty", "msg": "WithdrawQueue is empty"}, {"code": 28, "name": "InvalidParamsSet", "msg": "Params Set is invalid"}, {"code": 29, "name": "InvalidInput", "msg": "InvalidInput"}, {"code": 30, "name": "ExceededSlippage", "msg": "instruction exceeds desired slippage limit"}, {"code": 31, "name": "CalculationExRateFailure", "msg": "CalculationExRateFailure"}, {"code": 32, "name": "CheckedSubOverflow", "msg": "Checked_Sub Overflow"}, {"code": 33, "name": "CheckedAddOverflow", "msg": "Checked_Add Overflow"}, {"code": 34, "name": "CheckedMulOverflow", "msg": "Checked_Mul Overflow"}, {"code": 35, "name": "CheckedDivOverflow", "msg": "Checked_Div Overflow"}, {"code": 36, "name": "CheckedEmptyFunds", "msg": "Empty Funds"}, {"code": 37, "name": "CalcPnlError", "msg": "Calc pnl error"}, {"code": 38, "name": "InvalidSplTokenProgram", "msg": "InvalidSplTokenProgram"}, {"code": 39, "name": "TakePnlError", "msg": "Take Pnl error"}, {"code": 40, "name": "InsufficientFunds", "msg": "Insufficient funds"}, {"code": 41, "name": "ConversionFailure", "msg": "Conversion to u64 failed with an overflow or underflow"}, {"code": 42, "name": "InvalidUserToken", "msg": "user token input does not match amm"}, {"code": 43, "name": "InvalidSrmMint", "msg": "InvalidSrmMint"}, {"code": 44, "name": "InvalidSrmToken", "msg": "InvalidSrmToken"}, {"code": 45, "name": "TooManyOpenOrders", "msg": "TooManyOpenOrders"}, {"code": 46, "name": "OrderAtSlotIsPlaced", "msg": "OrderAtSlotIsPlaced"}, {"code": 47, "name": "InvalidSysProgramAddress", "msg": "InvalidSysProgramAddress"}, {"code": 48, "name": "InvalidFee", "msg": "The provided fee does not match the program owner's constraints"}, {"code": 49, "name": "RepeatCreateAmm", "msg": "Repeat create amm about market"}, {"code": 50, "name": "NotAllowZeroLP", "msg": "Not allow Zero LP"}, {"code": 51, "name": "InvalidCloseAuthority", "msg": "Token account has a close authority"}, {"code": 52, "name": "InvalidFreezeAuthority", "msg": "Pool token mint has a freeze authority"}, {"code": 53, "name": "InvalidReferPCMint", "msg": "InvalidReferPCMint"}, {"code": 54, "name": "InvalidConfigAccount", "msg": "InvalidConfigAccount"}, {"code": 55, "name": "RepeatCreateConfigAccount", "msg": "Repeat create staking config account"}, {"code": 56, "name": "UnknownAmmError", "msg": "Unknown <PERSON><PERSON>"}]}