logging:
  level: INFO   # Уровень логирования: DEBUG, INFO, WARNING, ERROR
  audit_file: "logs/audit.log"  # Путь к файлу аудита (все сделки, выходы, ошибки)

# Идентификация бота и параметры подключения
name: "bot-sniper-2"
env_file: ".env"

# Список RPC-эндпоинтов для балансировки нагрузки и отказоустойчивости (опционально)
# Если указан, перекрывает одиночные rpc_endpoint/wss_endpoint
# 🎯 ПОРЯДОК ВАЖЕН: Первые RPC используются для покупок/продаж, последние - для поиска токенов
rpc_endpoints:
  # 💰 ЛУЧШИЕ RPC ДЛЯ ПОКУПОК/ПРОДАЖ (по результатам тестирования, первые в списке)
  - url: "${CHAINSTACK_RPC_ENDPOINT}"
    wss: "${CHAINSTACK_WSS_ENDPOINT}"
  - url: "${ALCHEMY_RPC_ENDPOINT}"
    wss: "${ALCHEMY_WSS_ENDPOINT}"
  - url: "${QUICKNODE_RPC_ENDPOINT}"
    wss: "${QUICKNODE_WSS_ENDPOINT}"
  # 🔍 RPC ДЛЯ ПОИСКА ТОКЕНОВ И МОНИТОРИНГА (последние в списке)
  - url: "${HELIUS_RPC_ENDPOINT}"
    wss: "${HELIUS_WSS_ENDPOINT}"
  - url: "${SOLANA_NODE_RPC_ENDPOINT}"
    wss: "${SOLANA_NODE_WSS_ENDPOINT}"

# Одиночный RPC-эндпоинт (используется, если rpc_endpoints не задан)
rpc_endpoint: "${SOLANA_NODE_RPC_ENDPOINT}"
wss_endpoint: "${SOLANA_NODE_WSS_ENDPOINT}"
private_key: "${SOLANA_PRIVATE_KEY}"

enabled: true # Можно отключить бота, не удаляя конфиг
separate_process: true # Запускать в отдельном процессе



# Настройки Helius API (дополнительные возможности)
helius:
  api_key: "${HELIUS_API_KEY}"
  transactions_api: "https://api.helius.xyz/v0/transactions/?api-key=${HELIUS_API_KEY}"
  addresses_api: "https://api.helius.xyz/v0/addresses/{address}/transactions/?api-key=${HELIUS_API_KEY}"

# Торговые параметры
# Управление исполнением сделок: сумма SOL на сделку и допустимое отклонение цены
trade:
  buy_amount: 0.028 # Сумма SOL для покупки (в SOL)
  buy_slippage: 0.35 # Максимально допустимое отклонение цены (0.50 = 50%) - увеличено для pump.fun
  sell_slippage: 0.40 # ЭКСТРЕМАЛЬНО ВЫСОКИЙ slippage для продажи при падениях (80%)



  # 🧪 ТЕСТОВЫЙ РЕЖИМ - БЕЗ РЕАЛЬНЫХ ПОКУПОК/ПРОДАЖ
  dry_run: false # true = симуляция, false = реальная торговля

  # Настройки стратегии выхода
  exit_strategy: "tp_sl" # Варианты: "time_based", "tp_sl", "manual"
  take_profit_percentage: 999 # Фиксация прибыли при росте на 10%
  stop_loss_percentage: 0.05 # Стоп-лосс при падении на 10%
  pre_trailing_stop_loss: null # Стоп-лосс на +12% до активации трейлинга - ОТКЛЮЧЕНО
  # Activity-Based Hold Time - умное время удержания на основе активности токена
  hold_time:
    activity_based: true
    max_absolute: 3600        # Абсолютный максимум (60 минут)
    inactivity_timeout: 120   # Выход если нет активности 120 сек (2 мин)
    price_change_threshold: 2 # Минимальное изменение цены для "активности" (%)
    check_interval: 5         # Базовая проверка activity каждые 5 сек (НЕ для цены!)

    smart_intervals:
      high_activity: 2        # Активный токен - анализ каждые 2 сек
      low_activity: 5         # Неактивный - анализ каждые 5 сек
      stagnant: 10           # Стагнация - анализ каждые 10 сек
  price_check_interval: 0.4 # ОСНОВНОЙ мониторинг цены каждые 0.4 сек (единственный источник данных)

  # Настройки трейлинг-стопа (опционально)
  trailing_stop_percentage: 0.08 # Продать, если цена упала на 8% от пика после активации
  trailing_stop_activation: 0.25 # Активировать трейлинг-стоп после 25% прибыли

  # Расширенные настройки очереди и параллелизма
  max_queue_size: 100 # Максимальное количество токенов в очереди
  max_active_positions: 1 # Максимум одновременно торгуемых токенов

  # 🎯 DEDICATED RPC PER POSITION - каждая позиция получает свой RPC
  dedicated_rpc_per_position: true # Включить dedicated RPC для каждой позиции
  min_dedicated_rpcs: 3 # Минимум RPC для выделения позициям (Helius, QuickNode, Alchemy)
  reserve_rpcs_for_discovery: 2 # Количество RPC зарезервированных для поиска токенов (Chainstack + api.mainnet-beta.solana.com)

  # ⚡ TIMING SETTINGS - настройки задержек
  wait_time_after_creation: 1 # Задержка после создания токена (сек) - БЫЛО 15!

# Настройки приоритетных комиссий
# Управление скоростью и стоимостью транзакций в сети Solana.
# Внимание: динамический режим требует дополнительного запроса к RPC и замедляет покупку.
priority_fees:
  enable_dynamic: false # Использовать последние транзакции для оценки комиссии (getRecentPrioritizationFees)
  enable_fixed: true # Использовать фиксированную комиссию ниже
  fixed_amount: 1_000_000 # МАКСИМАЛЬНАЯ комиссия для ULTRA FAST продажи (увеличено с 2M до 5M)
  extra_percentage: 0.0 # Дополнительный процент к комиссии (0.1 = 10%)
  hard_cap: 1_000_000 # Максимально допустимая комиссия (микролампорты)

# Фильтры для отбора токенов
filters:
  min_price_hard_drop: 0.000000030 # Порог отсечения: токены с ценой ниже сразу отбрасываются

  # 🛡️ НАСТРОЙКИ ЗАЩИТЫ ОТ ОБВАЛОВ
  max_price_drop_threshold: 0.01 # Максимальное падение цены для отмены покупки (10% - более строгий)
  price_stability_check: true # Проверка стабильности цены перед покупкой
  min_stability_period: 15 # Минимальный период стабильной цены (секунды)
  min_growth_checks: 3 # Минимальное количество проверок стабильного роста
  min_growth_percentage: 0.001 # Минимальный процент роста для каждой проверки (2% = 0.02)

  # 📈 НАСТРОЙКИ АНАЛИЗА ТРЕНДА
  trend_tolerance: 0.07 # Толерантность к падениям цены (5% = 0.05) - падения меньше этого значения игнорируются

  # Фильтры по названию/символу - ОТКЛЮЧЕНЫ
  match_filter: null # Фильтр по названию/символу - ОТКЛЮЧЕНО
  blacklist: null # Черный список слов - ОТКЛЮЧЕНО
  whitelist: null # Белый список слов - ОТКЛЮЧЕНО

  # Пример: торговать только токенами, созданными этим адресом (фильтр по создателю)
  bro_address: null # например, "7Gk...abc", или null для отключения

  # Минимальный возраст токена (сек) — слишком молодые токены отправляются в отстойник
  min_token_age: 15 # Минимальный возраст токена (сек) — токены младше отправляются в отстойник (anti-snipe)
  max_token_age: 3600 # Максимальный возраст токена (сек) — токены старше 1 часа отбрасываются (было 0 - отбрасывал ВСЕ!)
  min_liquidity: null # Минимальная ликвидность пула (в SOL) — ОТКЛЮЧЕНО
  min_price: 0.000000039 # Минимальная цена токена (SOL) ≈ $0.0000010 при курсе $184/SOL
  # ВНИМАНИЕ: строго запрещено покупать токены ниже min_price! Все токены с ценой ниже min_price должны быть отброшены.
  max_price: 0.000000060 # Максимальная цена токена (SOL) ≈ $0.0000030 при курсе $184/SOL (в 3 раза выше min)
  min_holders: null # Минимальное количество holders — ОТКЛЮЧЕНО
  # Настройки отстойника для повторной проверки токенов:
  # recheck_delay: задержка перед повторной проверкой (сек)
  # max_recheck_attempts: максимальное число попыток переоценки токена
  # recheck_on_price_fail: отправлять ли в отстойник если не прошла цена, но прошли холдеры

  # Приоритетный фильтр: токены, соответствующие этим условиям, получают более высокий приоритет в очереди
  # Можно указать адреса создателей, символы или другие правила
  # priority_filter:
  #   creators:
  #     - "7Gk...abc"
  #     - "AnotherCreatorAddress"
  #   symbols:
  #     - "SOL"
  #     - "USDC"

  # (Продвинуто) Можно комбинировать фильтры: все должны пройти, чтобы токен был обработан

  # listener_type: "logs" # Метод обнаружения новых токенов: "logs", "blocks" или "pumpportal"
  listener_type: "logs"



  # YOLO mode: непрерывно торговать токенами
  yolo_mode: true


  # Настройки отстойника для переоценки (первый отстойник)
  recheck_delay: 1 # Задержка перед повторной проверкой фильтров (сек)
  max_recheck_attempts: 10 # Максимальное число попыток переоценки токена
  recheck_on_price_fail: true # Отправлять в отстойник если не прошла цена, но прошли холдеры

  # Настройки второго отстойника (price/holders quarantine)
  second_recheck_delay: 15 # Задержка перед повторной проверкой во втором отстойнике (сек)
  second_max_recheck_attempts: 2 # Максимальное число попыток во втором отстойнике

# Настройки повторов и таймингов

retries:
  max_attempts: 2 # Количество попыток отправки транзакции
  wait_after_creation: 0 # Задержка для стабилизации тестовых RPC (2 сек)
  wait_after_buy: 1 # Пауза после покупки токена
  wait_before_new_token: 2 # Пауза между сделками

  # 🚨 НАСТРОЙКИ ПОПЫТОК ПРОДАЖИ - ЗАЩИТА ОТ БЕСКОНЕЧНЫХ ПОПЫТОК
  max_sell_attempts: 4 # Максимальное количество попыток продажи одного токена
  sell_retry_delay: 2.0 # Задержка между попытками продажи (сек)
  abandon_after_minutes: 5 # Отказаться от продажи через N минут (защита от зависания)

# Дополнительные тайминги
timing:
  # Дополнительные тайминги:
  # token_wait_timeout: максимальное время ожидания для обработки отложенных токенов
  # scan_interval: интервал сканирования новых токенов
  # monitoring_interval: частота проверки цены купленных токенов для мгновенной реакции на TP/SL/Trailing Stop
  max_token_age: 3600 # Максимальный возраст токена (сек) — токены старше 1 часа отбрасываются
  token_wait_timeout: 120 # Время ожидания для обработки отложенных токенов
  #scan_interval: 15 # Интервал сканирования (не критично для WebSocket)
# monitoring_interval удален - не используется в коде, дублирует price_check_interval

# Управление токенами и аккаунтами
cleanup:
  # Режим очистки аккаунтов:
  # "disabled": очистка не выполняется
  # "on_fail": только при неудачной покупке
  # "after_sell": после продажи
  # "post_session": после завершения торговой сессии
  mode: "post_session"
  force_close_with_burn: false # Принудительно сжигать остатки токенов перед закрытием аккаунта
  with_priority_fee: false # Использовать приоритетные комиссии для очистки

# Настройки провайдера ноды (не реализовано)
node:
  max_rps: 25 # Увеличено для ULTRA FAST мониторинга (20 RPS + запас)
  max_tokens_per_scan: 12 # Максимум токенов за один проход (оптимизация для массового сканирования)
  cache_duration: 45 # Время кэширования результатов (сек)
  request_delay: 0.01 # Задержка между RPC-запросами для стабильности
