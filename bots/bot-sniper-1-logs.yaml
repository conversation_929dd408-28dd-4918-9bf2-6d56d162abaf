logging:
  level: INFO   # Уровень логирования: DEBUG, INFO, WARNING, ERROR
  audit_file: "logs/audit.log"  # Путь к файлу аудита (все сделки, выходы, ошибки)

# Идентификация бота и параметры подключения
name: "bot-sniper-1"
env_file: ".env"

# Список RPC-эндпоинтов для балансировки нагрузки и отказоустойчивости (опционально)
# Если указан, перекрывает одиночные rpc_endpoint/wss_endpoint
rpc_endpoints:
  - url: "${SOLANA_NODE_RPC_ENDPOINT}"
    wss: "${SOLANA_NODE_WSS_ENDPOINT}"
  # - url: "https://solana-rpc2.example.com"
  #   wss: "wss://solana-rpc2.example.com/ws"
  # Добавьте дополнительные эндпоинты при необходимости

# Одиночный RPC-эндпоинт (используется, если rpc_endpoints не задан)
rpc_endpoint: "${SOLANA_NODE_RPC_ENDPOINT}"
wss_endpoint: "${SOLANA_NODE_WSS_ENDPOINT}"
private_key: "${SOLANA_PRIVATE_KEY}"

enabled: true # Можно отключить бота, не удаляя конфиг
separate_process: true # Запускать в отдельном процессе



# Торговые параметры
# Управление исполнением сделок: сумма SOL на сделку и допустимое отклонение цены
trade:
  buy_amount: 0.001 # Сумма SOL для покупки (в SOL)
  buy_slippage: 0.1 # Максимально допустимое отклонение цены (0.3 = 30%)
  sell_slippage: 0.1 # Максимально допустимое отклонение цены при продаже

  # Настройки стратегии выхода
  exit_strategy: "time_based" # Варианты: "time_based", "tp_sl", "manual"
  take_profit_percentage: 0.1 # Фиксация прибыли при росте на 10%
  stop_loss_percentage: 0.1 # Стоп-лосс при падении на 10%
  max_hold_time: 100 # Максимальное время удержания позиции (сек)
  price_check_interval: 0.5 # Проверять цену каждые 2 сек

  # Настройки трейлинг-стопа (опционально)
  trailing_stop_percentage: 0.1 # Продать, если цена упала на 10% от пика после активации
  trailing_stop_activation: 0.15 # Активировать трейлинг-стоп после 15% прибыли



  # Расширенные настройки очереди и параллелизма
  max_queue_size: 100 # Максимальное количество токенов в очереди
  max_active_positions: 1 # Максимум одновременно торгуемых токенов

# Настройки приоритетных комиссий
# Управление скоростью и стоимостью транзакций в сети Solana.
# Внимание: динамический режим требует дополнительного запроса к RPC и замедляет покупку.
priority_fees:
  enable_dynamic: false # Использовать последние транзакции для оценки комиссии (getRecentPrioritizationFees)
  enable_fixed: true # Использовать фиксированную комиссию ниже
  fixed_amount: 300_000 # Базовая комиссия в микролампортах
  extra_percentage: 0.0 # Дополнительный процент к комиссии (0.1 = 10%)
  hard_cap: 300_000 # Максимально допустимая комиссия (микролампорты)

# Фильтры для отбора токенов
filters:
  bro_address: null
  min_token_age: 60
  max_token_age: 300
  min_price: 0.000000065
  max_price: 0.000000070
  min_holders: 15
  listener_type: logs

  yolo_mode: true
  recheck_delay: 18
  max_recheck_attempts: 3
  recheck_on_price_fail: true
  # recheck_delay: задержка перед повторной проверкой (сек)
  # max_recheck_attempts: максимальное число попыток переоценки токена
  # recheck_on_price_fail: отправлять ли в отстойник если не прошла цена, но прошли холдеры

  # Приоритетный фильтр: токены, соответствующие этим условиям, получают более высокий приоритет в очереди
  # Можно указать адреса создателей, символы или другие правила
  priority_filter:
    creators:
      - "7Gk...abc"
      - "AnotherCreatorAddress"
    symbols:
      - "SOL"
      - "USDC"

  # (Продвинуто) Можно комбинировать фильтры: все должны пройти, чтобы токен был обработан

  # listener_type: "logs" # Метод обнаружения новых токенов: "logs", "blocks" или "pumpportal"
  listener_type: "logs"



  # YOLO mode: непрерывно торговать токенами
  yolo_mode: true

  # Настройки отстойника для переоценки
  recheck_delay: 18 # Задержка перед повторной проверкой фильтров (сек)
  max_recheck_attempts: 3 # Максимальное число попыток переоценки токена
  recheck_on_price_fail: true # Отправлять в отстойник если не прошла цена, но прошли холдеры

# Настройки повторов и таймингов

retries:
  max_attempts: 2 # Количество попыток отправки транзакции
  wait_after_creation: 3 # Секунд ожидания после создания токена
  wait_after_buy: 3 # Пауза после покупки токена
  wait_before_new_token: 2 # Пауза между сделками

# Дополнительные тайминги
timing:
  # Дополнительные тайминги:
  # token_wait_timeout: максимальное время ожидания для обработки отложенных токенов
  # scan_interval: интервал сканирования новых токенов
  # monitoring_interval: частота проверки цены купленных токенов для мгновенной реакции на TP/SL/Trailing Stop
  token_wait_timeout: 120 # Время ожидания для обработки отложенных токенов
  scan_interval: 15 # Интервал сканирования (не критично для WebSocket)
  monitoring_interval: 0.5 # Проверка цены купленных токенов каждые 0.5 сек для мгновенной реакции на TP/SL/Trailing Stop

# Управление токенами и аккаунтами
cleanup:
  # Режим очистки аккаунтов:
  # "disabled": очистка не выполняется
  # "on_fail": только при неудачной покупке
  # "after_sell": после продажи
  # "post_session": после завершения торговой сессии
  mode: "post_session"
  force_close_with_burn: false # Принудительно сжигать остатки токенов перед закрытием аккаунта
  with_priority_fee: false # Использовать приоритетные комиссии для очистки

# Настройки провайдера ноды (не реализовано)
node:
  max_rps: 12 # Максимальное количество запросов в секунду (ограничение нагрузки на RPC)
  max_tokens_per_scan: 12 # Максимум токенов за один проход (оптимизация для массового сканирования)
  cache_duration: 45 # Время кэширования результатов (сек)
  request_delay: 0.1 # Задержка между RPC-запросами для стабильности
