#!/usr/bin/env python3
"""
Проверка баланса токена PIKO в кошельке
"""

import asyncio
import os
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient
from spl.token.instructions import get_associated_token_address

load_dotenv()

async def check_piko_balance():
    """Проверяет баланс токена PIKO в кошельке"""
    
    # Токен PIKO
    token_mint = "BaSiFGNtGvQiqQycgr5Ps782JbZh9fNHLEBjAp6cpump"
    expected_amount = 14836.132744
    
    # Получаем приватный ключ из .env
    private_key_str = os.getenv("SOLANA_PRIVATE_KEY")
    if not private_key_str:
        print("❌ SOLANA_PRIVATE_KEY не найден в .env файле!")
        return
    
    # Создаем кошелек
    try:
        private_key_bytes = base58.b58decode(private_key_str)
        keypair = Keypair.from_bytes(private_key_bytes)
        wallet_address = str(keypair.pubkey())
        print(f"🔑 Адрес кошелька: {wallet_address}")
        print(f"🪙 Токен PIKO: {token_mint}")
        print(f"📊 Ожидаемое количество: {expected_amount:.6f} токенов")
    except Exception as e:
        print(f"❌ Ошибка создания кошелька: {e}")
        return
    
    # Подключаемся к RPC
    rpc_endpoint = os.getenv("SOLANA_NODE_RPC_ENDPOINT", "https://api.mainnet-beta.solana.com")
    print(f"🌐 RPC endpoint: {rpc_endpoint}")
    
    try:
        async with AsyncClient(rpc_endpoint) as client:
            # Получаем associated token account
            mint_pubkey = Pubkey.from_string(token_mint)
            ata = get_associated_token_address(keypair.pubkey(), mint_pubkey)
            print(f"📍 Associated Token Account: {ata}")
            
            # Проверяем баланс токена
            try:
                token_account_info = await client.get_token_account_balance(ata)
                if token_account_info.value:
                    balance = token_account_info.value.amount
                    decimals = token_account_info.value.decimals
                    balance_decimal = int(balance) / (10 ** decimals)
                    
                    print(f"\n💰 РЕЗУЛЬТАТ ПРОВЕРКИ:")
                    print(f"   Фактический баланс: {balance_decimal:.6f} токенов")
                    print(f"   Raw amount: {balance}")
                    print(f"   Decimals: {decimals}")
                    
                    if balance_decimal > 0:
                        print(f"   ✅ ТОКЕН НАЙДЕН В КОШЕЛЬКЕ!")
                        
                        # Сравниваем с ожидаемым количеством
                        difference = abs(balance_decimal - expected_amount)
                        tolerance = expected_amount * 0.01  # 1% tolerance
                        
                        if difference <= tolerance:
                            print(f"   ✅ Количество соответствует ожидаемому")
                        else:
                            print(f"   ⚠️ Количество отличается от ожидаемого")
                            print(f"      Разница: {difference:.6f} токенов")
                            
                        print(f"\n🚨 ПРОБЛЕМА: БОТ НЕ СМОГ ПРОДАТЬ ТОКЕН!")
                        print(f"   Возможные причины:")
                        print(f"   1. Ошибка в логике продажи")
                        print(f"   2. Проблема с получением баланса при продаже")
                        print(f"   3. Ошибка в Associated Token Account")
                        print(f"   4. Проблема с RPC при продаже")
                        
                        # Проверяем информацию об аккаунте
                        try:
                            account_info = await client.get_account_info(ata)
                            if account_info.value:
                                print(f"\n📋 Информация об ATA:")
                                print(f"   Владелец: {account_info.value.owner}")
                                print(f"   Размер данных: {len(account_info.value.data)} байт")
                                print(f"   Executable: {account_info.value.executable}")
                                print(f"   Rent Epoch: {account_info.value.rent_epoch}")
                            else:
                                print(f"   ❌ Не удалось получить информацию об ATA")
                        except Exception as e:
                            print(f"   ❌ Ошибка получения информации об ATA: {e}")
                            
                    else:
                        print(f"   ❌ ТОКЕН НЕ НАЙДЕН (баланс = 0)")
                        
                else:
                    print(f"❌ Не удалось получить информацию о токене")
                    
            except Exception as e:
                if "could not find account" in str(e).lower():
                    print(f"❌ Associated Token Account не найден")
                    print(f"   🔍 Это означает, что токен не был куплен или ATA не создан")
                else:
                    print(f"❌ Ошибка получения баланса токена: {e}")
                
    except Exception as e:
        print(f"❌ Ошибка подключения к RPC: {e}")

if __name__ == "__main__":
    asyncio.run(check_piko_balance())
