#!/bin/bash

# Скрипт безопасной остановки pump.fun бота
# Использование: ./stop_bot.sh [--force]

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция логирования
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Проверка аргументов
FORCE_KILL=false
if [ "$1" = "--force" ]; then
    FORCE_KILL=true
    warning "Режим принудительной остановки активирован!"
fi

PID_FILE="bot.pid"

# Функция поиска процессов бота
find_bot_processes() {
    pgrep -f "bot_runner.py" 2>/dev/null || true
}

# Функция ожидания завершения процесса
wait_for_process_end() {
    local pid=$1
    local timeout=${2:-10}
    local count=0
    
    while [ $count -lt $timeout ]; do
        if ! kill -0 $pid 2>/dev/null; then
            return 0  # Процесс завершился
        fi
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    return 1  # Таймаут
}

log "Поиск активных процессов бота..."

# Поиск процессов
BOT_PIDS=$(find_bot_processes)

if [ -z "$BOT_PIDS" ]; then
    log "Активные процессы бота не найдены."
    
    # Проверка PID файла
    if [ -f "$PID_FILE" ]; then
        warning "Найден PID файл, но процесс не активен. Удаляю файл."
        rm -f "$PID_FILE"
    fi
    
    success "Бот не запущен."
    exit 0
fi

echo "Найдены процессы бота:"
echo "$BOT_PIDS" | while read pid; do
    if [ ! -z "$pid" ]; then
        ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null || echo "PID $pid (процесс завершился)"
    fi
done
echo ""

# Остановка процессов
for pid in $BOT_PIDS; do
    if [ ! -z "$pid" ]; then
        log "Остановка процесса PID: $pid"
        
        if [ "$FORCE_KILL" = true ]; then
            # Принудительная остановка
            warning "Принудительная остановка процесса $pid..."
            kill -9 $pid 2>/dev/null || true
        else
            # Мягкая остановка
            log "Отправка сигнала SIGTERM процессу $pid..."
            kill -TERM $pid 2>/dev/null || true
            
            # Ожидание завершения
            echo -n "Ожидание завершения процесса $pid"
            if wait_for_process_end $pid 15; then
                echo ""
                success "Процесс $pid завершился корректно."
            else
                echo ""
                warning "Процесс $pid не завершился за 15 секунд. Принудительная остановка..."
                kill -9 $pid 2>/dev/null || true
                sleep 2
                if kill -0 $pid 2>/dev/null; then
                    error "Не удалось остановить процесс $pid!"
                else
                    success "Процесс $pid принудительно остановлен."
                fi
            fi
        fi
    fi
done

# Финальная проверка
sleep 2
REMAINING_PIDS=$(find_bot_processes)

if [ ! -z "$REMAINING_PIDS" ]; then
    error "Некоторые процессы все еще активны:"
    echo "$REMAINING_PIDS" | while read pid; do
        if [ ! -z "$pid" ]; then
            ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null || true
        fi
    done
    echo ""
    echo "Для принудительной остановки используйте: ./stop_bot.sh --force"
    exit 1
fi

# Очистка PID файла
if [ -f "$PID_FILE" ]; then
    rm -f "$PID_FILE"
    log "PID файл удален."
fi

success "Все процессы бота успешно остановлены!"

# Показать статистику логов
if [ -d "logs" ]; then
    echo ""
    log "Статистика логов:"
    find logs -name "*.log" -exec ls -lh {} \; 2>/dev/null | head -5
    
    # Проверка на большие логи
    large_logs=$(find logs -name "*.log" -size +1M 2>/dev/null || true)
    if [ ! -z "$large_logs" ]; then
        echo ""
        warning "Найдены большие лог-файлы (>1MB):"
        echo "$large_logs" | while read file; do
            size=$(du -h "$file" | cut -f1)
            echo "  $file: $size"
        done
        echo ""
        echo "Рекомендуется очистить их командой: python cleanup_large_logs.py"
    fi
fi

echo ""
log "Бот полностью остановлен. Для запуска используйте: ./start_bot.sh"
