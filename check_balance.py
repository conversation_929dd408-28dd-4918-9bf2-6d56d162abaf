#!/usr/bin/env python3
"""
Скрипт для проверки баланса кошелька бота
"""

import asyncio
import os
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from solana.rpc.async_api import AsyncClient

load_dotenv()

async def check_wallet_balance():
    """Проверяет баланс SOL кошелька бота"""
    
    # Получаем приватный ключ из .env
    private_key_str = os.getenv("SOLANA_PRIVATE_KEY")
    if not private_key_str:
        print("❌ SOLANA_PRIVATE_KEY не найден в .env файле!")
        return
    
    # Создаем кошелек
    try:
        private_key_bytes = base58.b58decode(private_key_str)
        keypair = Keypair.from_bytes(private_key_bytes)
        wallet_address = str(keypair.pubkey())
        print(f"🔑 Адрес кошелька: {wallet_address}")
    except Exception as e:
        print(f"❌ Ошибка создания кошелька: {e}")
        return
    
    # Подключаемся к RPC
    rpc_endpoint = os.getenv("SOLANA_NODE_RPC_ENDPOINT", "https://api.mainnet-beta.solana.com")
    print(f"🌐 RPC endpoint: {rpc_endpoint}")
    
    try:
        async with AsyncClient(rpc_endpoint) as client:
            # Получаем баланс
            balance_response = await client.get_balance(keypair.pubkey())
            if balance_response.value is not None:
                balance_lamports = balance_response.value
                balance_sol = balance_lamports / 1_000_000_000  # Конвертируем в SOL
                
                print(f"💰 Баланс: {balance_sol:.9f} SOL ({balance_lamports:,} lamports)")
                
                # Проверяем достаточно ли средств для покупки
                buy_amount = 0.003  # SOL
                if balance_sol >= buy_amount:
                    print(f"✅ Достаточно средств для покупки {buy_amount} SOL")
                else:
                    print(f"❌ НЕДОСТАТОЧНО СРЕДСТВ! Нужно минимум {buy_amount} SOL")
                    print(f"   Не хватает: {buy_amount - balance_sol:.9f} SOL")
                
                # Проверяем минимальный баланс для комиссий
                min_balance = 0.005  # SOL для комиссий
                if balance_sol < min_balance:
                    print(f"⚠️  НИЗКИЙ БАЛАНС! Рекомендуется минимум {min_balance} SOL для комиссий")
                    
            else:
                print("❌ Не удалось получить баланс кошелька")
                
    except Exception as e:
        print(f"❌ Ошибка подключения к RPC: {e}")

if __name__ == "__main__":
    asyncio.run(check_wallet_balance())
