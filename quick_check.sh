#!/bin/bash
# ⚡ БЫСТРАЯ ПРОВЕРКА БОТА
# Автор: Pump.fun Trading Bot
# Дата: 2025-07-25

echo "⚡ БЫСТРАЯ ПРОВЕРКА БОТА"
echo "======================="

# Проверка статуса
echo "🔍 Статус бота:"
./status_bot.sh

echo ""
echo "🐍 Проверка синтаксиса кода:"
if python3 -m py_compile src/trading/trader.py src/trading/buyer.py; then
    echo "✅ Код компилируется без ошибок"
else
    echo "❌ Ошибки в коде!"
    exit 1
fi

echo ""
echo "📝 Проверка конфигурации:"
python3 -c "
import yaml
try:
    config=yaml.safe_load(open('bots/bot-sniper-2-logs.yaml'))
    print(f'✅ Buy amount: {config[\"trade\"][\"buy_amount\"]} SOL')
    print(f'✅ Safety enabled: {config[\"filters\"][\"price_stability_check\"]}')
    print(f'✅ Growth checks: {config[\"filters\"][\"min_growth_checks\"]}')
    print(f'✅ Price drop threshold: {config[\"filters\"][\"max_price_drop_threshold\"]}')
except Exception as e:
    print(f'❌ Ошибка конфигурации: {e}')
    exit(1)
"

echo ""
echo "💾 Свободное место:"
df -h . | grep -v Filesystem

echo ""
echo "🔑 Проверка приватного ключа:"
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()

key = os.getenv('SOLANA_PRIVATE_KEY')
if key:
    if len(key) == 88:
        print('✅ Приватный ключ: полный (88 символов)')
    else:
        print(f'🧪 Приватный ключ: тестовый ({len(key)} символов) - ТЕСТОВЫЙ РЕЖИМ')
else:
    print('❌ Приватный ключ не найден')
"

echo ""
echo "🚀 ГОТОВНОСТЬ:"
echo "✅ Все проверки пройдены"
echo "🧪 ТЕСТОВЫЙ РЕЖИМ: Покупки будут неудачными (для анализа)"
echo ""
echo "📊 КОМАНДЫ ДЛЯ ЗАПУСКА И МОНИТОРИНГА:"
echo "./start_bot.sh bots/bot-sniper-2-logs.yaml"
echo "tail -f logs/bot-sniper-2_*.log | grep -E \"INSTANT BUY|buy_error\""
