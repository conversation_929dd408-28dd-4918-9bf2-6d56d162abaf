# 📋 Руководство по логированию

## Проблема с большими лог-файлами

Visual Studio Code начинает тормозить или зависать при работе с лог-файлами размером более 5000 строк. Эта проблема решена с помощью автоматической ротации логов.

## ✅ Решение

### 1. Автоматическая ротация логов

Система логирования теперь автоматически ротирует файлы:
- **Максимальный размер файла**: 200KB (~1000 строк)
- **Количество backup файлов**: 10 для всех типов логов
- **Автоматическое архивирование**: больших файлов в `logs/archived/`

### 2. Мониторинг логов

Встроенный мониторинг отслеживает размеры файлов и автоматически архивирует большие файлы.

## 🛠️ Утилиты

### Проверка состояния логов
```bash
python check_logs_status.py
```

### Очистка больших файлов
```bash
python cleanup_large_logs.py
```

### Ручная проверка размеров
```bash
find logs/ -name "*.log" -exec wc -l {} \; | sort -nr
```

## 📁 Структура логов

```
logs/
├── audit.log                    # Торговые операции
├── bot-sniper-1_YYYYMMDD_HHMMSS.log  # Логи ботов
├── bot-sniper-2_YYYYMMDD_HHMMSS.log
└── archived/                    # Архивированные большие файлы
    ├── bot-sniper-1_20250720_184729_20250723_161238.gz
    └── ...
```

## ⚙️ Настройки

### В коде (src/utils/logger.py)
```python
# Настройки ротации
max_bytes = 200_000      # 200KB (~1000 строк)
backup_count = 10        # 10 backup файлов
```

### В конфигурации (logging_config.yaml)
```yaml
logging:
  rotation:
    max_bytes: 200000
    backup_count: 10
    audit_backup_count: 10
```

## 🔧 Интеграция с ботом

Система автоматически интегрирована в `bot_runner.py`:
- Мониторинг запускается при старте бота
- Отчеты о состоянии логов в консоли
- Автоматическое архивирование больших файлов

## 📊 Мониторинг

### Автоматический мониторинг
- Проверка каждые 5 минут
- Архивирование файлов >1000 строк
- Логирование операций

### Ручная проверка
```python
from src.utils.log_monitor import setup_log_monitoring

monitor = setup_log_monitoring()
print(monitor.get_summary_report())
```

## 🚨 Рекомендации

1. **Не открывайте файлы >1000 строк в VS Code** - используйте `tail`, `less` или архивируйте их
2. **Регулярно проверяйте состояние логов** с помощью `check_logs_status.py`
3. **Архивы сохраняются автоматически** в `logs/archived/`
4. **Старые архивы удаляются** через 30 дней

## 🔍 Диагностика проблем

### VS Code тормозит?
1. Проверьте размеры файлов: `python check_logs_status.py`
2. Архивируйте большие файлы: `python cleanup_large_logs.py`
3. Перезапустите VS Code

### Логи не ротируются?
1. Проверьте настройки в `src/utils/logger.py`
2. Убедитесь, что мониторинг запущен
3. Проверьте права доступа к директории `logs/`

### Нет места на диске?
1. Очистите старые архивы: `rm logs/archived/*.gz`
2. Уменьшите `backup_count` в настройках
3. Настройте автоматическое удаление старых архивов

## 📈 Статистика

Система ведет статистику:
- Количество отслеживаемых файлов
- Количество архивированных файлов
- Время последней проверки
- Общий размер логов

## 🔄 Обновления

### v1.0 (2025-07-23)
- ✅ Автоматическая ротация логов
- ✅ Мониторинг размеров файлов
- ✅ Архивирование больших файлов
- ✅ Интеграция с bot_runner
- ✅ Утилиты для управления логами
