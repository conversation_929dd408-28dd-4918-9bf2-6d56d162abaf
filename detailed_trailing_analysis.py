#!/usr/bin/env python3
"""
Детальный анализ трейлинг-стопа на основе реальных данных из логов.
"""

import sys
import os

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_trailing_stop_logic():
    """Анализирует логику трейлинг-стопа на основе реальных данных."""
    
    print("🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ТРЕЙЛИНГ-СТОПА")
    print("=" * 60)
    
    # Данные из реального лога токена UNDEFINED
    entry_price = 6.71e-08  # SOL
    take_profit_price = 7.63e-08  # +10%
    stop_loss_price = 6.24e-08   # -10%
    trailing_activation = entry_price * 1.15  # +15% = 7.72e-08
    trailing_stop_percent = 0.10  # 10%
    
    print(f"📊 РЕАЛЬНЫЕ ПАРАМЕТРЫ ТОКЕНА UNDEFINED:")
    print(f"   Entry Price: {entry_price:.2e} SOL")
    print(f"   Take Profit: {take_profit_price:.2e} SOL (+10%)")
    print(f"   Stop Loss: {stop_loss_price:.2e} SOL (-10%)")
    print(f"   Trailing Activation: {trailing_activation:.2e} SOL (+15%)")
    print(f"   Trailing Stop: {trailing_stop_percent*100}% от пика")
    print()
    
    # Реальная последовательность цен из лога
    real_price_sequence = [
        (6.71e-08, "Покупка"),
        (7.74e-08, "Максимум (+15.4%)"),
        (6.19e-08, "Продажа (-10.7%)")
    ]
    
    print("📈 РЕАЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ ЦЕН:")
    print("-" * 40)
    
    max_price = entry_price
    trailing_activated = False
    
    for price, event in real_price_sequence:
        profit = ((price - entry_price) / entry_price) * 100
        
        # Обновляем максимальную цену
        if price > max_price:
            max_price = price
        
        # Проверяем активацию трейлинг-стопа
        if not trailing_activated and price >= trailing_activation:
            trailing_activated = True
            print(f"   🎯 АКТИВАЦИЯ ТРЕЙЛИНГ-СТОПА при {price:.2e} SOL (+{profit:.1f}%)")
        
        # Определяем, что должно сработать
        should_exit = False
        exit_reason = None
        
        if trailing_activated:
            # Трейлинг-стоп активен
            trailing_stop_price = max_price * (1 - trailing_stop_percent)
            drop_from_peak = ((max_price - price) / max_price) * 100 if max_price > 0 else 0
            
            if price <= trailing_stop_price:
                should_exit = True
                exit_reason = "TRAILING_STOP"
            
            print(f"   {event}: {price:.2e} SOL (+{profit:.1f}%) | "
                  f"Max: {max_price:.2e} | "
                  f"Trailing Stop: {trailing_stop_price:.2e} | "
                  f"Drop: -{drop_from_peak:.1f}% | "
                  f"Exit: {'✅ ' + exit_reason if should_exit else '❌'}")
        else:
            # Трейлинг-стоп не активен, проверяем обычные условия
            if price >= take_profit_price:
                should_exit = True
                exit_reason = "TAKE_PROFIT"
            elif price <= stop_loss_price:
                should_exit = True
                exit_reason = "STOP_LOSS"
            
            print(f"   {event}: {price:.2e} SOL (+{profit:.1f}%) | "
                  f"Trailing: ❌ | "
                  f"Exit: {'✅ ' + exit_reason if should_exit else '❌'}")
    
    print()
    
    # Анализ различных сценариев
    print("🎯 АНАЛИЗ РАЗЛИЧНЫХ СЦЕНАРИЕВ:")
    print("-" * 40)
    
    scenarios = [
        {
            "name": "Медленный рост до TP",
            "prices": [6.71e-08, 7.00e-08, 7.30e-08, 7.63e-08],
            "description": "Цена растет медленно до тейк-профита"
        },
        {
            "name": "Резкий рост с активацией трейлинг",
            "prices": [6.71e-08, 7.80e-08, 8.50e-08, 7.65e-08],
            "description": "Цена резко растет >15%, активируется трейлинг"
        },
        {
            "name": "Супер рост (x2)",
            "prices": [6.71e-08, 13.42e-08, 12.08e-08],
            "description": "Цена удваивается, трейлинг активируется мгновенно"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        print(f"   {scenario['description']}")
        
        max_price = entry_price
        trailing_activated = False
        
        for i, price in enumerate(scenario['prices']):
            profit = ((price - entry_price) / entry_price) * 100
            
            # Обновляем максимальную цену
            if price > max_price:
                max_price = price
            
            # Проверяем активацию трейлинг-стопа
            if not trailing_activated and price >= trailing_activation:
                trailing_activated = True
            
            # Определяем выход
            should_exit = False
            exit_reason = None
            
            if trailing_activated:
                trailing_stop_price = max_price * (1 - trailing_stop_percent)
                if price <= trailing_stop_price:
                    should_exit = True
                    exit_reason = "TRAILING_STOP"
                elif price >= take_profit_price:
                    # Трейлинг активен, но цена выше TP - продолжаем трейлинг
                    pass
            else:
                if price >= take_profit_price:
                    should_exit = True
                    exit_reason = "TAKE_PROFIT"
                elif price <= stop_loss_price:
                    should_exit = True
                    exit_reason = "STOP_LOSS"
            
            status = "✅ " + exit_reason if should_exit else "❌"
            trailing_status = "✅" if trailing_activated else "❌"
            
            print(f"   Шаг {i+1}: {price:.2e} SOL (+{profit:.1f}%) | "
                  f"Trailing: {trailing_status} | Exit: {status}")
            
            if should_exit:
                print(f"   🔥 ВЫХОД: {exit_reason}")
                break
    
    print()
    print("🎯 КЛЮЧЕВЫЕ ВЫВОДЫ:")
    print("1. 🚀 Трейлинг-стоп активируется при росте >15%")
    print("2. 🎯 После активации трейлинг МИНУЕТ обычный тейк-профит")
    print("3. 📈 Трейлинг отслеживает МАКСИМАЛЬНУЮ цену")
    print("4. 📉 Продажа при падении на 10% от пика")
    print("5. ⚡ При резком росте можно получить прибыль >10%")

if __name__ == "__main__":
    analyze_trailing_stop_logic()
