#!/bin/bash

# Скрипт безопасного запуска pump.fun бота
# Использование: ./start_bot.sh [config_file]

set -e  # Остановить при ошибке

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция логирования
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Проверка аргументов
CONFIG_FILE=${1:-"bots/bot-sniper-2-logs.yaml"}

if [ ! -f "$CONFIG_FILE" ]; then
    error "Конфигурационный файл не найден: $CONFIG_FILE"
    echo "Доступные конфигурации:"
    ls -1 bots/*.yaml 2>/dev/null || echo "Нет файлов конфигурации в папке bots/"
    exit 1
fi

# Проверка, что бот уже не запущен
if pgrep -f "bot_runner.py" > /dev/null; then
    error "Бот уже запущен!"
    echo "Активные процессы бота:"
    ps aux | grep "bot_runner.py" | grep -v grep
    echo ""
    echo "Для остановки используйте: ./stop_bot.sh"
    exit 1
fi

# Проверка виртуального окружения
if [ ! -d "venv" ]; then
    error "Виртуальное окружение не найдено!"
    echo "Создайте его командой: python3 -m venv venv"
    exit 1
fi

if [ ! -f "venv/bin/python" ]; then
    error "Python не найден в виртуальном окружении!"
    exit 1
fi

# Проверка зависимостей
log "Проверка зависимостей..."
if ! ./venv/bin/python -c "import solana, aiohttp, asyncio" 2>/dev/null; then
    error "Не все зависимости установлены!"
    echo "Установите их командой: ./venv/bin/pip install -r requirements.txt"
    exit 1
fi

# Проверка .env файла
if [ ! -f ".env" ]; then
    warning ".env файл не найден! Убедитесь, что все переменные окружения настроены."
fi

# Создание директории для логов
mkdir -p logs
mkdir -p logs/archived

# Проверка размеров существующих логов
log "Проверка размеров логов..."
large_logs=$(find logs -name "*.log" -size +500k 2>/dev/null || true)
if [ ! -z "$large_logs" ]; then
    warning "Найдены большие лог-файлы (>500KB):"
    echo "$large_logs" | while read file; do
        size=$(du -h "$file" | cut -f1)
        lines=$(wc -l < "$file" 2>/dev/null || echo "?")
        echo "  $file: $size ($lines строк)"
    done
    echo ""
    echo "Рекомендуется очистить их командой: python cleanup_large_logs.py"
    read -p "Продолжить запуск? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Создание PID файла
PID_FILE="bot.pid"

# Функция очистки при выходе
cleanup() {
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
}
trap cleanup EXIT

log "Запуск бота с конфигурацией: $CONFIG_FILE"
log "Для остановки используйте: ./stop_bot.sh"
log "Для мониторинга логов: tail -f logs/bot-sniper-*.log"
echo ""

# Запуск бота в фоне с перенаправлением вывода
PYTHONPATH=. nohup ./venv/bin/python src/bot_runner.py --config "$CONFIG_FILE" > bot_output.log 2>&1 &

# Сохранение PID
BOT_PID=$!
echo $BOT_PID > "$PID_FILE"

# Ожидание запуска
sleep 3

# Проверка, что процесс запустился
if kill -0 $BOT_PID 2>/dev/null; then
    success "Бот успешно запущен! PID: $BOT_PID"
    echo ""
    echo "📊 Полезные команды:"
    echo "  Остановить бота:     ./stop_bot.sh"
    echo "  Статус бота:         ./status_bot.sh"
    echo "  Просмотр логов:      tail -f logs/bot-sniper-*.log"
    echo "  Просмотр вывода:     tail -f bot_output.log"
    echo "  Проверка логов:      python check_logs_status.py"
    echo ""
    echo "🔍 Мониторинг:"
    echo "  Процессы:           ps aux | grep bot_runner"
    echo "  Размеры логов:      ls -lh logs/*.log"
    echo ""
else
    error "Не удалось запустить бота!"
    echo "Проверьте bot_output.log для деталей:"
    tail -20 bot_output.log
    exit 1
fi
