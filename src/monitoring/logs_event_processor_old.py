"""
Event processing for pump.fun tokens using logsSubscribe data.
"""

import base64
import struct
from typing import Final

import base58
from solders.pubkey import Pubkey

from src.core.pubkeys import PumpAddresses, SystemAddresses
from src.trading.base import TokenInfo
from src.utils.logger import get_logger

logger = get_logger(__name__)


class LogsEventProcessor:
    """Processes events from pump.fun program logs."""

    # Discriminators for create instruction to avoid non-create transactions
    CREATE_DISCRIMINATOR_OLD: Final[int] = 8530921459188068891   # Старый дискриминатор (277 байт)
    CREATE_DISCRIMINATOR_MAIN: Final[int] = 6429692120587761128  # ОСНОВНОЙ дискриминатор (120 байт)
    CREATE_DISCRIMINATOR_OTHER: Final[int] = 12613666723233464954 # Другой дискриминатор
    CREATE_DISCRIMINATOR_NEW1: Final[int] = 15152637976602424557  # Новый дискриминатор (112 байт)
    CREATE_DISCRIMINATOR_NEW2: Final[int] = 11727207949415140238  # Новый дискриминатор (144 байт)
    CREATE_DISCRIMINATORS: Final[set] = {
        CREATE_DISCRIMINATOR_OLD,
        CREATE_DISCRIMINATOR_MAIN,
        CREATE_DISCRIMINATOR_OTHER,
        CREATE_DISCRIMINATOR_NEW1,
        CREATE_DISCRIMINATOR_NEW2
    }

    def __init__(self, pump_program: Pubkey):
        """Initialize event processor.

        Args:
            pump_program: Pump.fun program address
        """
        self.pump_program = pump_program

    def process_program_logs(self, logs: list[str], signature: str) -> TokenInfo | None:
        """Process program logs and extract token info.

        Args:
            logs: List of log strings from the notification
            signature: Transaction signature

        Returns:
            TokenInfo if a token creation is found, None otherwise
        """
        # ИСПРАВЛЕНО: НЕ фильтруем по тексту "Create" - pump.fun может не выводить такой лог!
        # Вместо этого проверяем дискриминатор в Program data (как в тестовом скрипте)

        # ПРАВИЛЬНАЯ ФИЛЬТРАЦИЯ: Ищем InitializeMint2 (как в рабочем коде)
        if not any("Instruction: InitializeMint2" in log for log in logs):
            return None

        # Ищем Program data в логах
        for log in logs:
            if "Program data: " in log and not log.startswith("Program data: vdt/"):
                try:
                    program_data_base64 = log.split("Program data: ")[1]
                    program_data_bytes = base64.b64decode(program_data_base64)

                    # Используем РАБОЧИЙ парсер
                    token_info = self._parse_event_data_working(program_data_bytes, signature)
                    if token_info:
                        return token_info

                except Exception as e:
                    logger.debug(f"Failed to parse program data: {e}")
                    continue

        return None

        # Skip Buy/Sell transactions - they are not token creations
        if any("Program log: Instruction: Buy" in log for log in logs):
            return None
        if any("Program log: Instruction: Sell" in log for log in logs):
            return None

        # Skip token account creation (not token creation)
        if any("Program log: Instruction: CreateTokenAccount" in log for log in logs):
            return None

        # Find and process program data - token creation is identified by discriminator
        for log in logs:
            if "Program data:" in log:
                try:
                    encoded_data = log.split(": ")[1]
                    decoded_data = base64.b64decode(encoded_data)
                    parsed_data = self._parse_create_instruction(decoded_data)

                    if parsed_data and "name" in parsed_data:
                        mint = Pubkey.from_string(parsed_data["mint"])
                        bonding_curve = Pubkey.from_string(parsed_data["bondingCurve"])
                        associated_curve = self._find_associated_bonding_curve(
                            mint, bonding_curve
                        )
                        creator = Pubkey.from_string(parsed_data["creator"])
                        creator_vault = self._find_creator_vault(creator)

                        return TokenInfo(
                            name=parsed_data["name"],
                            symbol=parsed_data["symbol"],
                            uri=parsed_data["uri"],
                            mint=mint,
                            bonding_curve=bonding_curve,
                            associated_bonding_curve=associated_curve,
                            user=Pubkey.from_string(parsed_data["user"]),
                            creator=creator,
                            creator_vault=creator_vault,
                        )
                except Exception as e:
                    logger.debug(f"Failed to process log data (not a token creation): {e}")

        return None

    def _parse_create_instruction(self, data: bytes) -> dict | None:
        """Parse the create instruction data.

        Args:
            data: Raw instruction data

        Returns:
            Dictionary of parsed data or None if parsing fails
        """
        if len(data) < 8:
            return None

        # Check for the correct instruction discriminator
        discriminator = struct.unpack("<Q", data[:8])[0]

        # Проверяем дискриминатор CREATE токена
        if discriminator not in self.CREATE_DISCRIMINATORS:
            return None

        # SUCCESS! Found CREATE discriminator
        logger.info(f"🎉 FOUND CREATE TOKEN! Discriminator: {discriminator}")

        offset = 8
        parsed_data = {}

        # Parse fields based on CreateEvent structure
        fields = [
            ("name", "string"),
            ("symbol", "string"),
            ("uri", "string"),
            ("mint", "publicKey"),
            ("bondingCurve", "publicKey"),
            ("user", "publicKey"),
            ("creator", "publicKey"),
        ]

        try:
            for field_name, field_type in fields:
                if field_type == "string":
                    length = struct.unpack("<I", data[offset : offset + 4])[0]
                    offset += 4
                    value = data[offset : offset + length].decode("utf-8")
                    offset += length
                elif field_type == "publicKey":
                    value = base58.b58encode(data[offset : offset + 32]).decode("utf-8")
                    offset += 32

                parsed_data[field_name] = value

            return parsed_data
        except Exception as e:
            logger.error(f"Failed to parse create instruction: {e}")
            return None

    def _find_associated_bonding_curve(
        self, mint: Pubkey, bonding_curve: Pubkey
    ) -> Pubkey:
        """
        Find the associated bonding curve for a given mint and bonding curve.
        This uses the standard ATA derivation.

        Args:
            mint: Token mint address
            bonding_curve: Bonding curve address

        Returns:
            Associated bonding curve address
        """
        derived_address, _ = Pubkey.find_program_address(
            [
                bytes(bonding_curve),
                bytes(SystemAddresses.TOKEN_PROGRAM),
                bytes(mint),
            ],
            SystemAddresses.ASSOCIATED_TOKEN_PROGRAM,
        )
        return derived_address
    
    def _find_creator_vault(self, creator: Pubkey) -> Pubkey:
        """
        Find the creator vault for a creator.

        Args:
            creator: Creator address

        Returns:
            Creator vault address
        """
        derived_address, _ = Pubkey.find_program_address(
            [
                b"creator-vault",
                bytes(creator)
            ],
            PumpAddresses.PROGRAM,
        )
        return derived_address

    def _detect_create_token_universal(self, logs: list[str], signature: str) -> TokenInfo | None:
        """Универсальный детектор CREATE токенов без зависимости от дискриминаторов.

        Анализирует:
        1. Наличие Program data
        2. Размер данных (CREATE токены имеют определенный размер)
        3. Структуру данных (mint address, metadata)
        4. Логи создания аккаунтов
        """
        # Ищем Program data в логах
        program_data_candidates = []

        for log in logs:
            if "Program data:" in log:
                try:
                    data_part = log.split("Program data: ")[1]
                    data = base64.b64decode(data_part)

                    # CREATE токены обычно имеют данные размером 100+ байт
                    if len(data) >= 100:
                        program_data_candidates.append(data)

                        # Логируем найденные дискриминаторы для анализа
                        if len(data) >= 8:
                            discriminator = struct.unpack('<Q', data[:8])[0]
                            logger.info(f"🔍 CANDIDATE CREATE TOKEN: discriminator={discriminator}, size={len(data)}, signature={signature[:20]}...")

                except Exception as e:
                    continue

        # Анализируем каждого кандидата
        for data in program_data_candidates:
            token_info = self._try_parse_create_token(data, signature)
            if token_info:
                return token_info

        return None

    def _try_parse_create_token(self, data: bytes, signature: str) -> TokenInfo | None:
        """Пытается распарсить данные как CREATE токен."""
        try:
            if len(data) < 8:
                return None

            discriminator = struct.unpack('<Q', data[:8])[0]

            # Проверяем известные дискриминаторы
            if discriminator in self.CREATE_DISCRIMINATORS:
                logger.info(f"🎉 KNOWN CREATE TOKEN! Discriminator: {discriminator}")
                return self._parse_create_token_data(data, signature)

            # Пытаемся парсить как потенциальный CREATE токен
            # даже с неизвестным дискриминатором
            token_info = self._parse_create_token_data(data, signature)
            if token_info and self._validate_token_info(token_info):
                logger.info(f"🎉 NEW CREATE TOKEN! Discriminator: {discriminator}")
                # Добавляем новый дискриминатор в список
                self.CREATE_DISCRIMINATORS.add(discriminator)
                return token_info

        except Exception as e:
            logger.debug(f"Failed to parse potential CREATE token: {e}")

        return None

    def _validate_token_info(self, token_info: TokenInfo) -> bool:
        """Валидирует, что TokenInfo содержит корректные данные CREATE токена."""
        try:
            # Проверяем обязательные поля
            if not token_info.name or not token_info.symbol or not token_info.mint:
                return False

            # Проверяем, что mint address валидный
            if len(str(token_info.mint)) != 44:  # Base58 длина Solana address
                return False

            # Проверяем разумность имени и символа
            if len(token_info.name) > 100 or len(token_info.symbol) > 20:
                return False

            return True

        except Exception:
            return False

    def _parse_create_token_data(self, data: bytes, signature: str) -> TokenInfo | None:
        """Парсит данные CREATE токена из Program data - РАБОЧАЯ ВЕРСИЯ."""
        try:
            if len(data) < 8:
                return None

            # Используем ПРОВЕРЕННЫЙ парсер из listen_new_direct_full_details.py
            parsed_data = self._parse_create_instruction_working(data)

            if not parsed_data:
                logger.warning(f"Failed to parse token data")
                return None

            # Создаем TokenInfo из распарсенных данных
            mint = Pubkey.from_string(parsed_data["mint"])
            bonding_curve = Pubkey.from_string(parsed_data["bondingCurve"])
            associated_curve = self._find_associated_bonding_curve(mint, bonding_curve)
            creator = Pubkey.from_string(parsed_data["creator"])
            creator_vault = self._find_creator_vault(creator)

            token_info = TokenInfo(
                name=parsed_data["name"],
                symbol=parsed_data["symbol"],
                uri=parsed_data["uri"],
                mint=mint,
                bonding_curve=bonding_curve,
                associated_bonding_curve=associated_curve,
                user=Pubkey.from_string(parsed_data["user"]),
                creator=creator,
                creator_vault=creator_vault,
            )

            logger.info(f"✅ TOKEN PARSED: {token_info.name} ({token_info.symbol}) - {token_info.mint}")
            return token_info

        except Exception as e:
            logger.error(f"Failed to parse CREATE token data: {e}")
            return None

    def _parse_event_data_working(self, data_bytes: bytes, signature: str) -> TokenInfo | None:
        """РАБОЧИЙ парсер из Medium поста - использует length-prefixed strings"""
        try:
            offset = 8  # Пропускаем дискриминатор

            def read_length_prefixed_string(data, offset):
                """Читает строку с префиксом длины (4 байта длина + данные)"""
                length = struct.unpack('<I', data[offset:offset + 4])[0]
                offset += 4
                string_data = data[offset:offset + length]
                offset += length
                return string_data.decode('utf-8').strip('\x00'), offset

            def read_pubkey(data, offset):
                """Читает Pubkey (32 байта) и конвертирует в строку"""
                pubkey_data = data[offset:offset + 32]
                offset += 32
                pubkey = str(Pubkey.from_bytes(pubkey_data))
                return pubkey, offset

            # Парсим данные в правильном порядке
            name, offset = read_length_prefixed_string(data_bytes, offset)
            symbol, offset = read_length_prefixed_string(data_bytes, offset)
            uri, offset = read_length_prefixed_string(data_bytes, offset)
            mint_str, offset = read_pubkey(data_bytes, offset)
            bonding_curve_str, offset = read_pubkey(data_bytes, offset)
            user_str, offset = read_pubkey(data_bytes, offset)

            # Создаем TokenInfo
            mint = Pubkey.from_string(mint_str)
            bonding_curve = Pubkey.from_string(bonding_curve_str)
            user = Pubkey.from_string(user_str)

            # Вычисляем associated bonding curve и creator vault
            associated_curve = self._find_associated_bonding_curve(mint, bonding_curve)
            creator_vault = self._find_creator_vault(user)

            token_info = TokenInfo(
                name=name,
                symbol=symbol,
                uri=uri,
                mint=mint,
                bonding_curve=bonding_curve,
                associated_bonding_curve=associated_curve,
                user=user,
                creator=user,  # creator = user
                creator_vault=creator_vault,
            )

            logger.info(f"✅ TOKEN PARSED: {name} ({symbol}) - {mint}")
            return token_info

        except Exception as e:
            logger.error(f"Failed to parse event data: {e}")
            return None
