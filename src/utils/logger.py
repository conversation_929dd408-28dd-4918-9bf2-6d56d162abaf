"""
Logging utilities for the pump.fun trading bot.
"""

import logging
from logging.handlers import RotatingFileHandler

# Global dict to store loggers
_loggers: dict[str, logging.Logger] = {}


def get_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """Get or create a logger with the given name.

    Args:
        name: Logger name, typically __name__
        level: Logging level

    Returns:
        Configured logger
    """
    global _loggers

    if name in _loggers:
        return _loggers[name]

    logger = logging.getLogger(name)
    logger.setLevel(level)

    _loggers[name] = logger
    return logger


def setup_file_logging(
    filename: str = "pump_trading.log",
    level: int = logging.INFO,
    max_bytes: int = 200_000,  # Уменьшено до 200KB (~1000 строк)
    backup_count: int = 10     # Уменьшено до 10 backup файлов
) -> None:
    """Set up file logging for all loggers with aggressive rotation.

    Args:
        filename: Log file path
        level: Logging level for file handler
        max_bytes: Maximum file size before rotation (default: 500KB)
        backup_count: Number of backup files to keep (default: 20)
    """
    root_logger = logging.getLogger()

    # Check if file handler with same filename already exists
    for handler in root_logger.handlers:
        if isinstance(handler, logging.FileHandler) and handler.baseFilename == filename:
            return  # File handler already added

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    file_handler = RotatingFileHandler(filename, maxBytes=max_bytes, backupCount=backup_count)
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)

    root_logger.addHandler(file_handler)


def get_audit_logger(
    audit_file: str = "logs/audit.log",
    max_bytes: int = 200_000,  # 200KB для audit логов (~1000 строк)
    backup_count: int = 10     # Уменьшено до 10 backup файлов
) -> logging.Logger:
    """Get or create a dedicated audit logger for trading actions with aggressive rotation."""
    logger = logging.getLogger("audit")
    if not any(isinstance(h, logging.FileHandler) and h.baseFilename == audit_file for h in logger.handlers):
        handler = RotatingFileHandler(audit_file, maxBytes=max_bytes, backupCount=backup_count)
        handler.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.propagate = False
    return logger
