"""
Автоматический мониторинг размера лог-файлов.
Предотвращает проблемы с VS Code при работе с большими файлами.
"""

import asyncio
import logging
import os
import gzip
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional


class LogMonitor:
    """Мониторинг и автоматическая ротация лог-файлов."""
    
    def __init__(
        self,
        logs_dir: str = "logs",
        max_lines: int = 3000,
        check_interval: int = 300,  # 5 минут
        auto_archive: bool = True
    ):
        """
        Инициализация мониторинга логов.
        
        Args:
            logs_dir: Директория с логами
            max_lines: Максимальное количество строк в файле
            check_interval: Интервал проверки в секундах
            auto_archive: Автоматически архивировать большие файлы
        """
        self.logs_dir = Path(logs_dir)
        self.max_lines = max_lines
        self.check_interval = check_interval
        self.auto_archive = auto_archive
        self.archive_dir = self.logs_dir / "archived"
        self.archive_dir.mkdir(exist_ok=True)
        
        # Статистика
        self.stats = {
            "files_monitored": 0,
            "files_archived": 0,
            "total_checks": 0,
            "last_check": None
        }
        
        self.logger = logging.getLogger(__name__)
    
    def count_lines_in_file(self, file_path: Path) -> int:
        """Подсчитать количество строк в файле."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return sum(1 for _ in f)
        except Exception as e:
            self.logger.error(f"Ошибка при подсчете строк в {file_path}: {e}")
            return 0
    
    def archive_large_file(self, file_path: Path) -> bool:
        """
        Архивировать большой лог-файл.
        
        Args:
            file_path: Путь к файлу для архивирования
            
        Returns:
            True если файл был архивирован, False если нет
        """
        try:
            # Создаем имя архивного файла
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"{file_path.stem}_{timestamp}.gz"
            archive_path = self.archive_dir / archive_name
            
            # Сжимаем файл
            with open(file_path, 'rb') as f_in:
                with gzip.open(archive_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Удаляем оригинальный файл
            file_path.unlink()
            
            self.logger.info(f"Архивирован большой лог-файл: {file_path.name} -> {archive_name}")
            self.stats["files_archived"] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Ошибка при архивировании {file_path}: {e}")
            return False
    
    def check_file_size(self, file_path: Path) -> Dict[str, any]:
        """
        Проверить размер файла и вернуть статистику.
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            Словарь со статистикой файла
        """
        line_count = self.count_lines_in_file(file_path)
        file_size = file_path.stat().st_size if file_path.exists() else 0
        
        return {
            "path": file_path,
            "lines": line_count,
            "size_bytes": file_size,
            "size_kb": file_size / 1024,
            "is_large": line_count > self.max_lines,
            "needs_action": line_count > self.max_lines and self.auto_archive
        }
    
    def scan_logs_directory(self) -> List[Dict[str, any]]:
        """
        Сканировать директорию с логами.
        
        Returns:
            Список статистики по всем лог-файлам
        """
        if not self.logs_dir.exists():
            self.logger.warning(f"Директория логов {self.logs_dir} не существует")
            return []
        
        results = []
        log_files = list(self.logs_dir.glob("*.log"))
        
        for log_file in log_files:
            file_stats = self.check_file_size(log_file)
            results.append(file_stats)
            
            # Автоматическое архивирование если включено
            if file_stats["needs_action"]:
                self.logger.warning(
                    f"Файл {log_file.name} превышает лимит: "
                    f"{file_stats['lines']} строк (лимит: {self.max_lines})"
                )
                
                if self.auto_archive:
                    self.archive_large_file(log_file)
        
        self.stats["files_monitored"] = len(log_files)
        self.stats["total_checks"] += 1
        self.stats["last_check"] = datetime.now().isoformat()
        
        return results
    
    def get_summary_report(self) -> str:
        """Получить сводный отчет о состоянии логов."""
        results = self.scan_logs_directory()
        
        if not results:
            return "📁 Лог-файлы не найдены"
        
        large_files = [r for r in results if r["is_large"]]
        total_size_mb = sum(r["size_bytes"] for r in results) / (1024 * 1024)
        
        report = [
            "📊 Отчет о состоянии лог-файлов:",
            f"   Всего файлов: {len(results)}",
            f"   Больших файлов (>{self.max_lines} строк): {len(large_files)}",
            f"   Общий размер: {total_size_mb:.2f} MB",
            f"   Файлов архивировано: {self.stats['files_archived']}",
            ""
        ]
        
        if large_files:
            report.append("⚠️ Большие файлы:")
            for file_info in large_files:
                report.append(
                    f"   {file_info['path'].name}: {file_info['lines']} строк "
                    f"({file_info['size_kb']:.1f} KB)"
                )
        else:
            report.append("✅ Все файлы в пределах нормы")
        
        return "\n".join(report)
    
    async def start_monitoring(self) -> None:
        """Запустить непрерывный мониторинг логов."""
        self.logger.info(
            f"Запуск мониторинга логов: директория={self.logs_dir}, "
            f"лимит={self.max_lines} строк, интервал={self.check_interval}с"
        )
        
        while True:
            try:
                results = self.scan_logs_directory()
                large_files = [r for r in results if r["is_large"]]
                
                if large_files:
                    self.logger.warning(
                        f"Обнаружено {len(large_files)} больших лог-файлов"
                    )
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Ошибка в мониторинге логов: {e}")
                await asyncio.sleep(60)  # Пауза при ошибке
    
    def cleanup_old_archives(self, days_old: int = 30) -> int:
        """
        Очистить старые архивы.
        
        Args:
            days_old: Возраст архивов для удаления (в днях)
            
        Returns:
            Количество удаленных файлов
        """
        if not self.archive_dir.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
        deleted_count = 0
        
        for archive_file in self.archive_dir.glob("*.gz"):
            if archive_file.stat().st_mtime < cutoff_time:
                try:
                    archive_file.unlink()
                    deleted_count += 1
                    self.logger.info(f"Удален старый архив: {archive_file.name}")
                except Exception as e:
                    self.logger.error(f"Ошибка при удалении {archive_file}: {e}")
        
        return deleted_count


# Функция для интеграции с основным ботом
def setup_log_monitoring(
    logs_dir: str = "logs",
    max_lines: int = 3000,
    check_interval: int = 300
) -> LogMonitor:
    """
    Настроить мониторинг логов для бота.
    
    Args:
        logs_dir: Директория с логами
        max_lines: Максимальное количество строк
        check_interval: Интервал проверки в секундах
        
    Returns:
        Экземпляр LogMonitor
    """
    monitor = LogMonitor(
        logs_dir=logs_dir,
        max_lines=max_lines,
        check_interval=check_interval,
        auto_archive=True
    )
    
    return monitor


# Пример использования
async def main():
    """Пример запуска мониторинга."""
    monitor = setup_log_monitoring()
    
    # Показать текущий отчет
    print(monitor.get_summary_report())
    
    # Запустить мониторинг (бесконечный цикл)
    # await monitor.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
