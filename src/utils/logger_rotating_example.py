import logging
from logging.handlers import RotatingFileHandler

# ...existing code...

def setup_file_logging(
    filename: str = "pump_trading.log", level: int = logging.INFO,
    max_bytes: int = 5_000_000, backup_count: int = 5
) -> None:
    """Set up file logging for all loggers with rotation.

    Args:
        filename: Log file path
        level: Logging level for file handler
        max_bytes: Max size in bytes before rotating
        backup_count: Number of rotated log files to keep
    """
    root_logger = logging.getLogger()

    # Check if file handler with same filename already exists
    for handler in root_logger.handlers:
        if isinstance(handler, logging.FileHandler) and handler.baseFilename == filename:
            return  # File handler already added

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    file_handler = RotatingFileHandler(filename, maxBytes=max_bytes, backupCount=backup_count)
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)

    root_logger.addHandler(file_handler)

# ...existing code for get_audit_logger...
