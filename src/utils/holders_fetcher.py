import aiohttp
from solders.pubkey import Pubkey

async def fetch_holders_count(rpc_url: str, mint: Pubkey) -> int | None:
    """
    Получить количество holders токена через getTokenLargestAccounts.
    """
    url = rpc_url
    headers = {"Content-Type": "application/json"}
    data = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenLargestAccounts",
        "params": [str(mint)]
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data, headers=headers, timeout=7) as resp:
            if resp.status != 200:
                return None
            result = await resp.json()
            if "result" in result and "value" in result["result"]:
                accounts = result["result"]["value"]
                # Считаем только те аккаунты, где amount > 0
                holders = sum(1 for acc in accounts if float(acc.get("amount", 0)) > 0)
                return holders
    return None
