import asyncio
import logging
import multiprocessing
from datetime import datetime
from pathlib import Path

import uvloop

asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

from src.config_loader import load_bot_config, print_config_summary
from src.trading.trader import PumpTrader
from src.utils.logger import setup_file_logging
from src.utils.log_monitor import setup_log_monitoring
from auto_log_cleanup import auto_cleanup_logs


def setup_logging(bot_name: str):
    """
    Set up logging to file for a specific bot instance with monitoring.

    Args:
        bot_name: Name of the bot for the log file
    """
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Автоматическая очистка логов при старте
    try:
        archived, deleted = auto_cleanup_logs(silent=True)
        if archived > 0 or deleted > 0:
            logging.info(f"Автоочистка логов: архивировано {archived}, удалено {deleted}")
    except Exception as e:
        logging.warning(f"Ошибка автоочистки логов: {e}")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = log_dir / f"{bot_name}_{timestamp}.log"

    # Настройка файлового логирования с улучшенной ротацией
    setup_file_logging(str(log_filename), max_bytes=200_000, backup_count=10)

    # 🔧 ОТКЛЮЧЕНИЕ ИЗБЫТОЧНОГО ЛОГИРОВАНИЯ HTTP ЗАПРОСОВ
    # Это предотвращает засорение логов строками "HTTP Request: POST https://..."
    logging.getLogger("httpx").setLevel(logging.WARNING)  # Только ошибки и предупреждения
    logging.getLogger("httpcore").setLevel(logging.WARNING)  # HTTP core библиотека
    logging.getLogger("urllib3").setLevel(logging.WARNING)  # Urllib3 библиотека
    logging.getLogger("requests").setLevel(logging.WARNING)  # Requests библиотека

    # Инициализация мониторинга логов
    log_monitor = setup_log_monitoring(logs_dir="logs", max_lines=1000, check_interval=300)

    # Показать текущий отчет о логах
    try:
        report = log_monitor.get_summary_report()
        logging.info(f"Состояние лог-файлов:\n{report}")
    except Exception as e:
        logging.warning(f"Не удалось получить отчет о логах: {e}")

    return log_monitor

async def start_bot(config_path: str):
    """
    Start a trading bot with the configuration from the specified path.
    
    Args:
        config_path: Path to the YAML configuration file
    """
    cfg = load_bot_config(config_path)
    log_monitor = setup_logging(cfg["name"])
    print_config_summary(cfg)
    
    # Multi-RPC endpoints (optional)
    rpc_endpoints = cfg.get("rpc_endpoints")

    # Audit log file path from config (default if not set)
    audit_log_file = cfg.get("logging", {}).get("audit_file", "logs/audit.log")

    trader = PumpTrader(
        # Connection settings
        rpc_endpoint=cfg.get("rpc_endpoint"),
        wss_endpoint=cfg.get("wss_endpoint"),
        rpc_endpoints=cfg.get("rpc_endpoints"),
        node_config=cfg.get("node", {}),
        private_key=cfg["private_key"],

        # Trade parameters
        buy_amount=cfg["trade"]["buy_amount"],
        buy_slippage=cfg["trade"]["buy_slippage"],
        sell_slippage=cfg["trade"].get("sell_slippage", 0.1),
        max_queue_size=cfg["trade"].get("max_queue_size", 100),
        max_active_positions=cfg["trade"].get("max_active_positions", 1),

        # RPC management parameters
        min_dedicated_rpcs=cfg["trade"].get("min_dedicated_rpcs"),
        reserve_rpcs_for_discovery=cfg["trade"].get("reserve_rpcs_for_discovery"),



        # Testing mode
        dry_run=cfg["trade"].get("dry_run", False),

        # Exit strategy configuration
        exit_strategy=cfg["trade"].get("exit_strategy", "time_based"),
        take_profit_percentage=cfg["trade"].get("take_profit_percentage"),
        stop_loss_percentage=cfg["trade"].get("stop_loss_percentage"),
        pre_trailing_stop_loss=cfg["trade"].get("pre_trailing_stop_loss"),
        max_hold_time=cfg["trade"].get("max_hold_time"),  # Legacy support
        hold_time_config=cfg["trade"].get("hold_time"),  # New activity-based config
        price_check_interval=cfg["trade"].get("price_check_interval", 10),
        trailing_stop_percentage=cfg["trade"].get("trailing_stop_percentage"),
        trailing_stop_activation=cfg["trade"].get("trailing_stop_activation"),
        filters_cfg=cfg.get("filters", {}),

        # Listener configuration
        listener_type=cfg["filters"].get("listener_type", "logs"),



        # PumpPortal configuration (if applicable)
        pumpportal_url=cfg.get("pumpportal", {}).get("url", "wss://pumpportal.fun/api/data"),

        # Priority fee configuration
        enable_dynamic_priority_fee=cfg.get("priority_fees", {}).get("enable_dynamic", False),
        enable_fixed_priority_fee=cfg.get("priority_fees", {}).get("enable_fixed", True),
        fixed_priority_fee=cfg.get("priority_fees", {}).get("fixed_amount", 500000),
        extra_priority_fee=cfg.get("priority_fees", {}).get("extra_percentage", 0.0),
        hard_cap_prior_fee=cfg.get("priority_fees", {}).get("hard_cap", 500000),

        # Retry and timeout settings
        max_retries=cfg.get("retries", {}).get("max_attempts", 10),
        wait_time_after_creation=cfg.get("retries", {}).get("wait_after_creation", 15),
        wait_time_after_buy=cfg.get("retries", {}).get("wait_after_buy", 15),
        wait_time_before_new_token=cfg.get("retries", {}).get("wait_before_new_token", 15),
        max_token_age=cfg.get("timing", {}).get("max_token_age", 0.001),
        token_wait_timeout=cfg.get("timing", {}).get("token_wait_timeout", 30),

        # Sell retry settings
        max_sell_attempts=cfg.get("retries", {}).get("max_sell_attempts", 10),
        sell_retry_delay=cfg.get("retries", {}).get("sell_retry_delay", 2.0),
        abandon_after_minutes=cfg.get("retries", {}).get("abandon_after_minutes", 5),

        # Cleanup settings
        cleanup_mode=cfg.get("cleanup", {}).get("mode", "disabled"),
        cleanup_force_close_with_burn=cfg.get("cleanup", {}).get("force_close_with_burn", False),
        cleanup_with_priority_fee=cfg.get("cleanup", {}).get("with_priority_fee", False),

        # Trading filters
        match_string=cfg["filters"].get("match_string"),
        bro_address=cfg["filters"].get("bro_address"),

        yolo_mode=cfg["filters"].get("yolo_mode", False),
        audit_log_file=audit_log_file,
    )
    
    await trader.start()

def run_bot_process(config_path):
    asyncio.run(start_bot(config_path))

def run_all_bots():
    """
    Run all bots defined in YAML files in the 'bots' directory.
    Only runs bots that have enabled=True (or where enabled is not specified).
    Bots can be run in separate processes based on their configuration.
    """
    bot_dir = Path("bots")
    if not bot_dir.exists():
        logging.error(f"Bot directory '{bot_dir}' not found")
        return
    
    bot_files = list(bot_dir.glob("*.yaml"))
    if not bot_files:
        logging.error(f"No bot configuration files found in '{bot_dir}'")
        return
    
    logging.info(f"Found {len(bot_files)} bot configuration files")
    
    processes = []
    skipped_bots = 0
    
    for file in bot_files:
        try:

            cfg = load_bot_config(str(file))
            bot_name = cfg.get("name", file.stem)
            # DEBUG: print full config for this bot
            logging.info(f"DEBUG FULL CONFIG for {file}: {cfg}")
            

            # DEBUG: print enabled status for each bot
            logging.info(f"DEBUG: {file} | name={bot_name} | enabled={cfg.get('enabled', 'not set')}")
            # Skip bots with enabled=False
            if not cfg.get("enabled", True):
                logging.info(f"Skipping disabled bot '{bot_name}'")
                skipped_bots += 1
                continue

            if cfg.get("separate_process", False):
                logging.info(f"Starting bot '{bot_name}' in separate process")
                p = multiprocessing.Process(
                    target=run_bot_process,
                    args=(str(file),),
                    name=f"bot-{bot_name}"
                )
                p.start()
                processes.append(p)
            else:
                logging.info(f"Starting bot '{bot_name}' in main process")
                asyncio.run(start_bot(str(file))) 
        except Exception as e:
            logging.exception(f"Failed to start bot from {file}: {e}")
    
    logging.info(f"Started {len(bot_files) - skipped_bots} bots, skipped {skipped_bots} disabled bots")
    
    for p in processes:
        p.join()
        logging.info(f"Process {p.name} completed")


import sys
import argparse

def main() -> None:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    parser = argparse.ArgumentParser(description="Pump.fun bot runner")
    parser.add_argument('--config', type=str, help='Path to YAML config file for single bot')
    parser.add_argument('positional_config', nargs='?', help='(Optional) Path to YAML config file as positional argument')
    args = parser.parse_args()

    config_path = args.config or args.positional_config
    if config_path:
        # Run single bot with given config
        asyncio.run(start_bot(config_path))
    else:
        # Run all bots in bots/ directory
        run_all_bots()

if __name__ == "__main__":
    main()