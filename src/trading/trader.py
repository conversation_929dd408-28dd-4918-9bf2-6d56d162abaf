"""
Main trading coordinator for pump.fun tokens.
Refactored PumpTrader to only process fresh tokens from WebSocket.
"""

import asyncio
import json
import os
import time
from datetime import datetime
from time import monotonic

import uvloop
from solders.pubkey import Pubkey

from src.cleanup.modes import (
    handle_cleanup_after_failure,
    handle_cleanup_after_sell,
    handle_cleanup_post_session,
)
from src.core.client import SolanaClient
from src.core.curve import BondingCurveManager
from src.core.dedicated_rpc_manager import DedicatedRpcManager
from src.core.multi_rpc import MultiRpcProvider
from src.core.priority_fee.manager import PriorityFeeManager
from src.core.pubkeys import PumpAddresses
from src.core.wallet import Wallet
from src.monitoring.block_listener import BlockListener

from src.monitoring.logs_listener import LogsListener
from src.monitoring.pumpportal_listener import PumpPortalListener
from src.trading.base import TokenInfo, TradeResult
from src.trading.buyer import TokenBuyer
from src.trading.exit_strategies import get_exit_strategy
from src.trading.filters import build_filters_from_config
from src.trading.position import Position, ExitReason, ActivityBasedPosition
from src.trading.seller import TokenSeller
from src.trading.token_queue import TokenQueue
from src.utils.logger import get_logger, get_audit_logger
from src.trading.delayed_token_queue import DelayedTokenQueue

asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

logger = get_logger(__name__)
# Отдельный logger для мониторинга (меньше спама в логах)
monitor_logger = get_logger(f"{__name__}.monitor")


class PumpTrader:
    """Coordinates trading operations for pump.fun tokens with focus on freshness."""
    def __init__(
        self,
        rpc_endpoint: str,
        wss_endpoint: str,
        private_key: str,
        buy_amount: float,
        buy_slippage: float,
        sell_slippage: float,
        listener_type: str = "logs",
        pumpportal_url: str = "wss://pumpportal.fun/api/data",


        # Testing configuration
        dry_run: bool = False,
        # Exit strategy configuration
        exit_strategy: str = "time_based",
        take_profit_percentage: float | None = None,
        stop_loss_percentage: float | None = None,
        pre_trailing_stop_loss: float | None = None,
        max_hold_time: int | None = None,  # Legacy support
        hold_time_config: dict | None = None,  # New activity-based config
        price_check_interval: int = 10,
        trailing_stop_percentage: float | None = None,
        trailing_stop_activation: float | None = None,
        # Priority fee configuration
        enable_dynamic_priority_fee: bool = False,
        enable_fixed_priority_fee: bool = True,
        fixed_priority_fee: int = 200_000,
        extra_priority_fee: float = 0.0,
        hard_cap_prior_fee: int = 200_000,
        # Retry and timeout settings
        max_retries: int = 3,
        wait_time_after_creation: int = 15, # here and further - seconds
        wait_time_after_buy: int = 15,
        wait_time_before_new_token: int = 15,
        max_token_age: int | float = 0.001,
        token_wait_timeout: int = 30,
        # Sell retry settings
        max_sell_attempts: int = 10,
        sell_retry_delay: float = 2.0,
        abandon_after_minutes: int = 5,
        # Cleanup settings
        cleanup_mode: str = "disabled",
        cleanup_force_close_with_burn: bool = False,
        cleanup_with_priority_fee: bool = False,
        # Trading filters
        match_string: str | None = None,
        bro_address: str | None = None,

        yolo_mode: bool = False,
        filters_cfg: dict = None,
        # RPC endpoints for multi-node support
        rpc_endpoints: list = None,
        node_config: dict = None,
        max_queue_size: int = 100,
        max_active_positions: int = 1,
        # RPC management parameters
        min_dedicated_rpcs: int = None,
        reserve_rpcs_for_discovery: int = None,
        audit_log_file: str = "logs/audit.log"
    ):
        """Initialize the pump trader.
        Args:
            rpc_endpoint: RPC endpoint URL
            wss_endpoint: WebSocket endpoint URL
            private_key: Wallet private key
            buy_amount: Amount of SOL to spend on buys
            buy_slippage: Slippage tolerance for buys
            sell_slippage: Slippage tolerance for sells
        from src.trading.delayed_token_queue import DelayedTokenQueue
        self.delayed_token_queue = DelayedTokenQueue()
        self.delayed_worker_task = None

            listener_type: Type of listener to use ('logs', 'blocks', or 'pumpportal')
            pumpportal_url: PumpPortal WebSocket URL (default: wss://pumpportal.fun/api/data)



            exit_strategy: Exit strategy ("time_based", "tp_sl", or "manual")
            take_profit_percentage: Take profit percentage (0.5 = 50% profit)
            stop_loss_percentage: Stop loss percentage (0.2 = 20% loss)
            max_hold_time: Maximum hold time in seconds
            price_check_interval: How often to check price for TP/SL (seconds)
        max_queue_size: int = 100,
        max_active_positions: int = 1,
        audit_log_file: str = "logs/audit.log"

            enable_dynamic_priority_fee: Whether to enable dynamic priority fees
            enable_fixed_priority_fee: Whether to enable fixed priority fees
            fixed_priority_fee: Fixed priority fee amount
            extra_priority_fee: Extra percentage for priority fees
            hard_cap_prior_fee: Hard cap for priority fees

            max_retries: Maximum number of retry attempts
            wait_time_after_creation: Time to wait after token creation (seconds)
            wait_time_after_buy: Time to wait after buying a token (seconds)
            wait_time_before_new_token: Time to wait before processing a new token (seconds)
            max_token_age: Maximum age of token to process (seconds)
            token_wait_timeout: Timeout for waiting for a token in single-token mode (seconds)

            cleanup_mode: Cleanup mode ("disabled", "auto", or "manual")
            cleanup_force_close_with_burn: Whether to force close with burn during cleanup
            cleanup_with_priority_fee: Whether to use priority fees during cleanup
            
            match_string: Optional string to match in token name/symbol
            bro_address: Optional creator address to filter by

            yolo_mode: If True, trade continuously

            filters_cfg: Configuration for trading filters
        """
        self.delayed_token_queue = DelayedTokenQueue()
        self.delayed_worker_task = None

        # Сохраняем параметры RPC управления
        self.min_dedicated_rpcs = min_dedicated_rpcs
        self.reserve_rpcs_for_discovery = reserve_rpcs_for_discovery

        if rpc_endpoints and isinstance(rpc_endpoints, list) and len(rpc_endpoints) > 0:
            # Читаем настройки из конфига или используем значения по умолчанию
            if node_config is None:
                node_config = {}
            max_rps = node_config.get('max_rps', 12)
            request_delay = node_config.get('request_delay', 0.05)
            self.multi_rpc_provider = MultiRpcProvider(rpc_endpoints, max_rps=max_rps, request_delay=request_delay)
            self.solana_client = SolanaClient(multi_rpc_provider=self.multi_rpc_provider)
            logger.info(f"Multi-RPC mode enabled: {len(rpc_endpoints)} endpoints")
            for idx, ep in enumerate(rpc_endpoints):
                logger.info(f"  RPC #{idx+1}: {ep.get('url')}  WSS: {ep.get('wss','-')}")
            current_rpc = self.multi_rpc_provider.get_current().get('url')
            logger.info(f"Current active RPC: {current_rpc}")

            # 🎯 Инициализируем DedicatedRpcManager для позиций
            # Используем настройки из конфигурации, если они заданы
            config_min_dedicated = getattr(self, 'min_dedicated_rpcs', None)
            config_reserve_discovery = getattr(self, 'reserve_rpcs_for_discovery', None)

            if config_min_dedicated is not None and config_reserve_discovery is not None:
                # Используем настройки из YAML конфигурации
                min_dedicated_rpcs = config_min_dedicated
                reserve_rpcs_for_discovery = config_reserve_discovery
                logger.info(f"🎯 Используем настройки из конфигурации: min_dedicated={min_dedicated_rpcs}, reserve_discovery={reserve_rpcs_for_discovery}")
            else:
                # Fallback к старой логике
                min_dedicated_rpcs = min(max_active_positions, len(rpc_endpoints) - 1)  # Оставляем 1 RPC для поиска
                reserve_rpcs_for_discovery = max(1, len(rpc_endpoints) - max_active_positions)  # Минимум 1 для поиска
                logger.info(f"🎯 Используем автоматические настройки: min_dedicated={min_dedicated_rpcs}, reserve_discovery={reserve_rpcs_for_discovery}")

            self.dedicated_rpc_manager = DedicatedRpcManager(
                rpc_endpoints=rpc_endpoints,
                min_dedicated_rpcs=min_dedicated_rpcs,
                reserve_rpcs_for_discovery=reserve_rpcs_for_discovery
            )

            # Создаем отдельный client для поиска токенов
            discovery_rpc_provider = self.dedicated_rpc_manager.get_discovery_rpc_provider()
            self.discovery_client = SolanaClient(multi_rpc_provider=discovery_rpc_provider)

            logger.info(f"🎯 DedicatedRpcManager: {max_active_positions} позиций, {reserve_rpcs_for_discovery} RPC для поиска")
        else:
            self.multi_rpc_provider = None
            self.solana_client = SolanaClient(rpc_endpoint)
            self.dedicated_rpc_manager = None
            self.discovery_client = self.solana_client  # Используем основной client

        self.wallet = Wallet(private_key)
        self.curve_manager = BondingCurveManager(self.solana_client)
        self.priority_fee_manager = PriorityFeeManager(
            client=self.solana_client,
            enable_dynamic_fee=enable_dynamic_priority_fee,
            enable_fixed_fee=enable_fixed_priority_fee,
            fixed_fee=fixed_priority_fee,
            extra_fee=extra_priority_fee,
            hard_cap=hard_cap_prior_fee,
        )
        


        # Initialize the appropriate listener type
        listener_type = listener_type.lower()
        if listener_type == "logs":
            # КРИТИЧНО: Используем только Solana.com WSS для LogsListener
            # Chainstack WSS не передает логи pump.fun!
            if self.multi_rpc_provider:
                # Ищем Solana.com WSS endpoint в списке
                solana_wss = None
                for endpoint in self.multi_rpc_provider.endpoints:
                    wss_url = endpoint.get('wss', '')
                    if 'api.mainnet-beta.solana.com' in wss_url:
                        solana_wss = wss_url
                        break

                if solana_wss:
                    logger.info(f"Using Solana.com WSS for LogsListener: {solana_wss}")
                    self.token_listener = LogsListener(solana_wss, PumpAddresses.PROGRAM)
                else:
                    # Fallback к первому WSS если Solana.com не найден
                    wss_url = self.multi_rpc_provider.next_wss()
                    logger.warning(f"Solana.com WSS not found, using fallback: {wss_url}")
                    self.token_listener = LogsListener(wss_url, PumpAddresses.PROGRAM)

                self._holders_rpc_url = self.multi_rpc_provider.get_current().get('url') if self.multi_rpc_provider else rpc_endpoint
            else:
                self.token_listener = LogsListener(wss_endpoint, PumpAddresses.PROGRAM)
                self._holders_rpc_url = rpc_endpoint
            logger.info("Using logsSubscribe listener for token monitoring")
        elif listener_type == "pumpportal":
            self.token_listener = PumpPortalListener(PumpAddresses.PROGRAM, pumpportal_url)
            logger.info("Using PumpPortal listener for token monitoring")
        else:
            self.token_listener = BlockListener(wss_endpoint, PumpAddresses.PROGRAM)
            logger.info("Using blockSubscribe listener for token monitoring")
            
        # Trading parameters
        self.buy_amount = buy_amount
        self.buy_slippage = buy_slippage
        self.sell_slippage = sell_slippage
        self.max_retries = max_retries

        self.dry_run = dry_run

        # Audit logger
        self.audit_logger = get_audit_logger(audit_log_file)
        
        # Exit strategy parameters
        self.exit_strategy_name = exit_strategy.lower()
        self.exit_strategy_impl = get_exit_strategy(self.exit_strategy_name)
        self.take_profit_percentage = take_profit_percentage
        self.stop_loss_percentage = stop_loss_percentage
        self.pre_trailing_stop_loss = pre_trailing_stop_loss
        self.max_hold_time = max_hold_time  # Legacy support
        self.hold_time_config = hold_time_config  # New activity-based config
        self.price_check_interval = price_check_interval
        self.trailing_stop_percentage = trailing_stop_percentage
        self.trailing_stop_activation = trailing_stop_activation
        
        # Timing parameters
        self.wait_time_after_creation = wait_time_after_creation
        self.wait_time_after_buy = wait_time_after_buy
        self.wait_time_before_new_token = wait_time_before_new_token
        self.max_token_age = max_token_age
        self.token_wait_timeout = token_wait_timeout

        # Sell retry parameters
        self.max_sell_attempts = max_sell_attempts
        self.sell_retry_delay = sell_retry_delay
        self.abandon_after_minutes = abandon_after_minutes
        
        # Cleanup parameters
        self.cleanup_mode = cleanup_mode
        self.cleanup_force_close_with_burn = cleanup_force_close_with_burn
        self.cleanup_with_priority_fee = cleanup_with_priority_fee

        # Trading filters/modes
        self.match_string = match_string
        self.bro_address = bro_address

        self.yolo_mode = yolo_mode

        # Filters configuration
        self.filters_cfg = filters_cfg or {}
        self.token_filter = build_filters_from_config(self.filters_cfg)

        # Create buyer and seller after filters_cfg is available
        self.buyer = TokenBuyer(
            self.solana_client,
            self.wallet,
            self.curve_manager,
            self.priority_fee_manager,
            buy_amount,
            buy_slippage,
            max_retries,

            min_price=self.filters_cfg.get("min_price"),
            max_price=self.filters_cfg.get("max_price"),
            dry_run=self.dry_run,
            max_price_drop_threshold=self.filters_cfg.get("max_price_drop_threshold", 0.15),
            price_stability_check=self.filters_cfg.get("price_stability_check", False),
            min_stability_period=self.filters_cfg.get("min_stability_period", 15),
            min_growth_checks=self.filters_cfg.get("min_growth_checks", 3),
            min_growth_percentage=self.filters_cfg.get("min_growth_percentage", 0.01)
        )
        self.seller = TokenSeller(
            self.solana_client,
            self.wallet,
            self.curve_manager,
            self.priority_fee_manager,
            sell_slippage,
            max_retries,
            dry_run=self.dry_run
        )
        
        # State tracking
        self.traded_mints: set[Pubkey] = set()
        # Параметры отстойника
        self.recheck_delay = self.filters_cfg.get("recheck_delay", 18)
        self.max_recheck_attempts = self.filters_cfg.get("max_recheck_attempts", 3)
        self.recheck_on_price_fail = self.filters_cfg.get("recheck_on_price_fail", True)
        self.second_recheck_delay = self.filters_cfg.get("second_recheck_delay", 30)
        self.second_max_recheck_attempts = self.filters_cfg.get("second_max_recheck_attempts", 2)
        self.token_queue = TokenQueue(
            maxsize=max_queue_size,
            max_active=max_active_positions,
            recheck_delay=self.recheck_delay,
            max_recheck_attempts=self.max_recheck_attempts,
            second_recheck_delay=self.second_recheck_delay,
            second_max_recheck_attempts=self.second_max_recheck_attempts
        )
        self.processing: bool = False
        self.processed_tokens: set[str] = set()
        self.token_timestamps: dict[str, float] = {}

        # 🛡️ Отслеживание истории цен для проверки стабильного роста
        self.price_history: dict[str, list] = {}  # {mint: [(timestamp, price), ...]}
        self.growth_checks: dict[str, int] = {}   # {mint: количество_проверок_роста}

        # 📊 Логгер для покупок
        self._setup_purchase_logger()

    def _setup_purchase_logger(self):
        """Настройка отдельного логгера для записи покупок в файл."""
        import logging
        from datetime import datetime

        # Создаем отдельный логгер для покупок
        self.purchase_logger = logging.getLogger('purchase_tracker')
        self.purchase_logger.setLevel(logging.INFO)

        # Убираем существующие обработчики чтобы избежать дублирования
        self.purchase_logger.handlers.clear()

        # Создаем файл с датой
        purchase_log_file = f"logs/purchases_{datetime.now().strftime('%Y%m%d')}.txt"

        # Создаем обработчик файла
        file_handler = logging.FileHandler(purchase_log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # Простой формат без времени (добавим время вручную)
        formatter = logging.Formatter('%(message)s')
        file_handler.setFormatter(formatter)

        self.purchase_logger.addHandler(file_handler)
        self.purchase_logger.propagate = False  # Не передавать в родительские логгеры

        # Записываем заголовок файла
        self.purchase_logger.info("=" * 80)
        self.purchase_logger.info(f"📊 ЖУРНАЛ ПОКУПОК ТОКЕНОВ - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.purchase_logger.info("=" * 80)

    def _log_purchase_attempt(self, token_info: TokenInfo, purchase_type: str, success: bool = True, error: str = None):
        """Логирование попытки покупки с детальной информацией."""
        from datetime import datetime
        from time import monotonic

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Получаем время первой и последней проверки
        token_key = str(token_info.mint)
        first_check_time = None
        last_check_time = None
        analysis_duration = None

        if token_key in self.token_timestamps:
            first_check_timestamp = self.token_timestamps[token_key]
            first_check_time = datetime.fromtimestamp(first_check_timestamp).strftime('%H:%M:%S')
            last_check_time = datetime.now().strftime('%H:%M:%S')
            analysis_duration = monotonic() - first_check_timestamp

        # Получаем информацию о тренде - ИСПРАВЛЕНО: показываем полную историю
        trend_info = "Нет данных"
        if hasattr(token_info, 'price_history') and token_info.price_history:
            history = token_info.price_history
            if len(history) >= 2:
                first_price = history[0]
                last_price = history[-1]
                overall_change = (last_price - first_price) / first_price * 100

                # Показываем полную историю цен для анализа
                if len(history) <= 5:
                    # Если цен мало - показываем все
                    price_sequence = " → ".join([f"{p:.2e}" for p in history])
                else:
                    # Если много цен - показываем первые 2, ..., последние 2
                    price_sequence = f"{history[0]:.2e} → {history[1]:.2e} → ... → {history[-2]:.2e} → {history[-1]:.2e}"

                # Добавляем информацию о падениях если они были
                trend_tolerance = self.filters_cfg.get("trend_tolerance", 0.05) * 100
                drops = []
                for i in range(1, len(history)):
                    change = (history[i] - history[i-1]) / history[i-1] * 100
                    if change < -trend_tolerance:
                        drops.append(f"#{i}: {change:.1f}%")

                drop_info = f" [ПАДЕНИЯ: {', '.join(drops)}]" if drops else ""
                trend_info = f"{price_sequence} (общий: {overall_change:+.1f}%){drop_info}"

        # Получаем толерантность
        trend_tolerance = self.filters_cfg.get("trend_tolerance", 0.05) * 100

        # Получаем информацию о росте и попытке покупки
        growth_info = "Нет данных"
        attempt_info = ""
        if hasattr(self, 'growth_checks') and token_info.mint in self.growth_checks:
            growth_count = self.growth_checks[token_info.mint]
            min_growth_checks = self.filters_cfg.get("min_growth_checks", 3)
            growth_info = f"{growth_count}/{min_growth_checks}"

            # Добавляем информацию о попытке покупки
            if hasattr(token_info, 'price_history') and token_info.price_history:
                attempt_num = len(token_info.price_history)
                max_attempts = self.filters_cfg.get("max_recheck_attempts", 10)
                attempt_info = f" (попытка {attempt_num}/{max_attempts})"

        # Формируем информацию о времени анализа
        time_info = ""
        if first_check_time and last_check_time and analysis_duration is not None:
            time_info = f"""
⏰ ПЕРВАЯ ПРОВЕРКА: {first_check_time}
⏱️ ПОСЛЕДНЯЯ ПРОВЕРКА: {last_check_time}
🕐 ВРЕМЯ АНАЛИЗА: {analysis_duration:.1f}с"""

        # Формируем запись
        status = "✅ УСПЕШНО" if success else f"❌ ОШИБКА: {error}"

        log_entry = f"""
{timestamp} | {status}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🪙 ТОКЕН: {token_info.symbol} ({token_info.name})
🔗 MINT: {token_info.mint}
💰 ЦЕНА: {getattr(token_info, 'price', 'Неизвестно')} SOL
📊 ТРЕНД: {trend_info}
🎯 ТОЛЕРАНТНОСТЬ: ±{trend_tolerance:.1f}%
📈 РОСТ: {growth_info}{attempt_info}{time_info}
🏷️ ТИП: {purchase_type}
👥 HOLDERS: {getattr(token_info, 'holders', 'Неизвестно')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""

        self.purchase_logger.info(log_entry)

    def _check_stable_growth(self, token_info: TokenInfo) -> bool:
        """
        Проверка стабильного роста токена.
        Требует min_growth_checks проверок подряд с ростом цены.
        """
        if not self.filters_cfg.get("price_stability_check", False):
            return True  # Проверка отключена

        mint = token_info.mint
        current_time = time.time()
        current_price = getattr(token_info, 'price', None)

        if current_price is None:
            return False

        # Инициализируем историю для нового токена
        if mint not in self.price_history:
            self.price_history[mint] = []
            self.growth_checks[mint] = 0

        # Добавляем текущую цену в историю
        self.price_history[mint].append((current_time, current_price))

        # Очищаем старые записи (старше min_stability_period)
        min_stability_period = self.filters_cfg.get("min_stability_period", 15)
        cutoff_time = current_time - min_stability_period
        self.price_history[mint] = [
            (t, p) for t, p in self.price_history[mint]
            if t >= cutoff_time
        ]

        # Нужно минимум 2 записи для сравнения
        if len(self.price_history[mint]) < 2:
            return False

        # Проверяем рост по сравнению с предыдущей ценой
        prev_price = self.price_history[mint][-2][1]

        # Вычисляем процент роста
        growth_percentage = (current_price - prev_price) / prev_price
        min_growth_percentage = self.filters_cfg.get("min_growth_percentage", 0.01)  # По умолчанию 1%

        if growth_percentage >= min_growth_percentage:
            self.growth_checks[mint] = self.growth_checks.get(mint, 0) + 1
            logger.info(f"🛡️ GROWTH CHECK: {token_info.symbol} рост {growth_percentage*100:.2f}% >= {min_growth_percentage*100:.1f}% ✅ ({self.growth_checks[mint]}/{self.filters_cfg.get('min_growth_checks', 3)})")
        else:
            # Рост недостаточный - сбрасываем счетчик
            self.growth_checks[mint] = 0
            logger.info(f"🛡️ GROWTH CHECK: {token_info.symbol} рост {growth_percentage*100:.2f}% < {min_growth_percentage*100:.1f}% ❌ (сброс счетчика)")

        min_growth_checks = self.filters_cfg.get("min_growth_checks", 3)
        has_stable_growth = self.growth_checks[mint] >= min_growth_checks

        if has_stable_growth:
            logger.info(f"🛡️ STABLE GROWTH CONFIRMED: {token_info.symbol} показал {self.growth_checks[mint]} проверок роста подряд (≥{self.filters_cfg.get('min_growth_percentage', 0.01)*100:.1f}% каждая)")

        return has_stable_growth

    async def start(self) -> None:
        """Start the trading bot and listen for new tokens."""
        logger.info("Starting pump.fun trader")
        logger.info(f"Match filter: {self.match_string if self.match_string else 'None'}")
        logger.info(f"Creator filter: {self.bro_address if self.bro_address else 'None'}")

        logger.info(f"YOLO mode: {self.yolo_mode}")
        logger.info(f"Exit strategy: {self.exit_strategy_name}")
        if self.exit_strategy_name == "tp_sl":
            logger.info(f"Take profit: {self.take_profit_percentage * 100 if self.take_profit_percentage else 'None'}%")
            logger.info(f"Stop loss: {self.stop_loss_percentage * 100 if self.stop_loss_percentage else 'None'}%")

        # 🧪 DRY RUN MODE INDICATOR
        if self.dry_run:
            logger.warning("🧪 DRY RUN MODE ENABLED - NO REAL TRADES WILL BE EXECUTED!")
            logger.warning("🧪 All buy/sell operations will be simulated only")
        else:
            logger.info("💰 LIVE TRADING MODE - Real trades will be executed")

            # Log hold time configuration
            if self.hold_time_config and self.hold_time_config.get("activity_based"):
                logger.info("Hold time: Activity-based")
                logger.info(f"  Max absolute: {self.hold_time_config.get('max_absolute', 3600)} seconds")
                logger.info(f"  Inactivity timeout: {self.hold_time_config.get('inactivity_timeout', 30)} seconds")
                logger.info(f"  Price change threshold: {self.hold_time_config.get('price_change_threshold', 5)}%")
            else:
                logger.info(f"Max hold time: {self.max_hold_time if self.max_hold_time else 'None'} seconds")
        logger.info(f"Max token age: {self.max_token_age} seconds")

        # Запуск воркера для delayed очереди
        self.delayed_worker_task = asyncio.create_task(self._delayed_worker())

        # Запуск мониторинга восстановления RPC (если multi-RPC включен)
        self.rpc_recovery_task = None
        if self.multi_rpc_provider:
            self.rpc_recovery_task = asyncio.create_task(self._rpc_recovery_monitor())
        try:
            health_resp = await self.solana_client.get_health()
            logger.info(f"RPC warm-up successful (getHealth passed: {health_resp})")
            # Log current RPC after warm-up if multi_rpc_provider is used
            if hasattr(self, 'multi_rpc_provider') and self.multi_rpc_provider:
                current_rpc = self.multi_rpc_provider.get_current().get('url')
                logger.info(f"Current active RPC after warm-up: {current_rpc}")
        except Exception as e:
            logger.warning(f"RPC warm-up failed: {e!s}")

        try:
            # Choose operating mode based on yolo_mode
            if not self.yolo_mode:
                # Single token mode: process one token and exit
                logger.info("Running in single token mode - will process one token and exit")
                token_info = await self._wait_for_token()
                if token_info:
                    await self._handle_token(token_info)
                    logger.info("Finished processing single token. Exiting...")
                else:
                    logger.info(f"No suitable token found within timeout period ({self.token_wait_timeout}s). Exiting...")
            else:
                # Continuous mode: process tokens until interrupted
                logger.info("Running in continuous mode - will process tokens until interrupted")
                processor_task = asyncio.create_task(
                    self._process_token_queue()
                )

                try:
                    min_holders = self.filters_cfg.get("min_holders")
                    await self.token_listener.listen_for_tokens(
                        lambda token: self._queue_token(token),
                        self.match_string,
                        self.bro_address,
                        self._holders_rpc_url,
                        min_holders,
                    )
                except Exception as e:
                    logger.error(f"Token listening stopped due to error: {e!s}")
                finally:
                    processor_task.cancel()
                    try:
                        await processor_task
                    except asyncio.CancelledError:
                        pass
        
        except Exception as e:
            logger.error(f"Trading stopped due to error: {e!s}")
        
        finally:
            await self._cleanup_resources()
            logger.info("Pump trader has shut down")

    async def _wait_for_token(self) -> TokenInfo | None:
        """Wait for a single token to be detected.
        
        Returns:
            TokenInfo or None if timeout occurs
        """
        # Create a one-time event to signal when a token is found
        token_found = asyncio.Event()
        found_token = None
        
        async def token_callback(token: TokenInfo) -> None:
            nonlocal found_token
            token_key = str(token.mint)
            # Сохраняем время первого обнаружения токена только если его ещё нет
            if token_key not in self.token_timestamps:
                self.token_timestamps[token_key] = monotonic()
            # Only process if not already processed and fresh
            if token_key not in self.processed_tokens:
                found_token = token
                self.processed_tokens.add(token_key)
                token_found.set()

        listener_task = asyncio.create_task(
            self.token_listener.listen_for_tokens(
                token_callback,
                self.match_string,
                self.bro_address,
                self._holders_rpc_url,
            )
        )
        
        # Wait for a token with a timeout
        try:
            logger.info(f"Waiting for a suitable token (timeout: {self.token_wait_timeout}s)...")
            await asyncio.wait_for(token_found.wait(), timeout=self.token_wait_timeout)
            logger.info(f"Found token: {found_token.symbol} ({found_token.mint})")
            return found_token
        except TimeoutError:
            logger.info(f"Timed out after waiting {self.token_wait_timeout}s for a token")
            return None
        finally:
            listener_task.cancel()
            try:
                await listener_task
            except asyncio.CancelledError:
                pass

    async def _rpc_recovery_monitor(self) -> None:
        """Мониторинг и восстановление заблокированных RPC."""
        try:
            while True:
                await asyncio.sleep(30)  # Проверка каждые 30 секунд
                if self.multi_rpc_provider:
                    self.multi_rpc_provider.check_recovery()
        except asyncio.CancelledError:
            logger.info("RPC recovery monitor stopped")
        except Exception as e:
            logger.error(f"RPC recovery monitor error: {e}")

    async def _cleanup_resources(self) -> None:
        """Perform cleanup operations before shutting down."""
        if self.traded_mints:
            try:
                logger.info(f"Cleaning up {len(self.traded_mints)} traded token(s)...")
                await handle_cleanup_post_session(
                    self.solana_client, 
                    self.wallet, 
                    list(self.traded_mints), 
                    self.priority_fee_manager,
                    self.cleanup_mode,
                    self.cleanup_with_priority_fee,
                    self.cleanup_force_close_with_burn
                )
            except Exception as e:
                logger.error(f"Error during cleanup: {e!s}")
                
        # old_keys = {k for k in self.token_timestamps if k not in self.processed_tokens}
        # for key in old_keys:
        #     self.token_timestamps.pop(key, None)
        # Остановка задач мониторинга
        if self.delayed_worker_task:
            self.delayed_worker_task.cancel()
            try:
                await self.delayed_worker_task
            except asyncio.CancelledError:
                pass

        if self.rpc_recovery_task:
            self.rpc_recovery_task.cancel()
            try:
                await self.rpc_recovery_task
            except asyncio.CancelledError:
                pass

        # Не удаляем время первого обнаружения токенов, которые ещё могут быть обработаны (например, в отстойнике)
        # Удаление времени теперь происходит только при окончательном отказе от токена
        await self.solana_client.close()

    async def _queue_token(
        self, token_info: TokenInfo
    ) -> None:
        """Queue a token for processing if not already processed.
        Args:
            token_info: Token information to queue
        """
        token_key = str(token_info.mint)

        if token_key in self.processed_tokens:
            logger.debug(f"Token {token_info.symbol} already processed. Skipping...")
            return

        # Сохраняем время первого обнаружения токена, не перезаписываем при повторном добавлении
        if token_key not in self.token_timestamps:
            self.token_timestamps[token_key] = monotonic()

        # Audit log: token found
        self.audit_logger.info(json.dumps({
            "event": "token_found",
            **token_info.to_dict(),
            "time": datetime.utcnow().isoformat()
        }))

        # Определяем приоритет токена
        priority = 100
        pf = self.filters_cfg.get("priority_filter", {})
        creators = set((pf.get("creators") or []))
        symbols = set((pf.get("symbols") or []))
        if (token_info.creator in creators) or (token_info.symbol in symbols):
            priority = 0  # Высший приоритет

        # --- Логика задержки для молодых токенов ---
        min_token_age = self.filters_cfg.get("min_token_age", 0)
        detected_time = self.token_timestamps[token_key]
        age = monotonic() - detected_time
        if min_token_age and age < min_token_age:
            delay = min_token_age - age
            logger.info(f"Token {token_info.symbol} ({token_info.mint}) слишком молод ({age:.2f}s), откладываем на {delay:.2f}s")
            await self.delayed_token_queue.add(token_info, delay)
            self.audit_logger.info(json.dumps({
                "event": "delayed_queue_add",
                **token_info.to_dict(),
                "delay": delay,
                "age": age,
                "min_token_age": min_token_age,
                "time": datetime.utcnow().isoformat()
            }))
        else:
            await self.token_queue.put(token_info, priority=priority)
            logger.info(f"Queued new token: {token_info.symbol} ({token_info.mint})")
    async def _delayed_worker(self):
        """Воркер для delayed_token_queue: перемещает созревшие токены в основную очередь онлайн."""
        while True:
            token = await self.delayed_token_queue.get_next_ready()
            token_key = str(token.mint)
            # Повторная проверка processed_tokens
            if token_key in self.processed_tokens:
                continue
            priority = 100
            pf = self.filters_cfg.get("priority_filter", {})
            creators = set((pf.get("creators") or []))
            symbols = set((pf.get("symbols") or []))
            if (token.creator in creators) or (token.symbol in symbols):
                priority = 0
            await self.token_queue.put(token, priority=priority)
            logger.info(f"[DELAYED QUEUE] Token {token.symbol} ({token.mint}) созрел и перемещён в основную очередь")
            self.audit_logger.info(json.dumps({
                "event": "delayed_queue_release",
                **token.to_dict(),
                "time": datetime.utcnow().isoformat()
            }))

    async def _process_token_queue(self) -> None:
        print("[AGE DEBUG PRINT] _process_token_queue STARTED")
        """Обработка токенов из очереди с поддержкой отстойника и повторной фильтрации."""
        while True:
            try:
                # Сначала обработать токены из отстойника, если готовы
                for token_info, attempts, detected_time, quarantine_type in self.token_queue.get_quarantine_ready():
                    # --- ВСЕГДА обновляем цену перед фильтрацией ---
                    logger.info(f"[PRICE DEBUG] Попытка получить цену для {token_info.symbol} (mint: {token_info.mint}), bonding_curve={token_info.bonding_curve}")
                    try:
                        price = await self.curve_manager.calculate_price(token_info.bonding_curve)
                        if price is None:
                            logger.warning(f"[TOKEN MIGRATED] {token_info.symbol} (mint: {token_info.mint}) bonding curve not found - token migrated or deleted")
                            # Удаляем токен из очереди - он больше не доступен для торговли
                            continue
                        token_info.price = price
                        logger.info(f"[PRICE REFRESH] {token_info.symbol} (mint: {token_info.mint}) price={price} (recheck {attempts+1})")
                    except Exception as e:
                        token_info.price = None
                        logger.error(f"[PRICE ERROR] Не удалось обновить цену для {token_info.symbol} (mint: {token_info.mint}), bonding_curve={token_info.bonding_curve}: {e!s}", exc_info=True)
                    token_key = str(token_info.mint)
                    # Если есть сохранённое время — не затираем, иначе восстанавливаем
                    if token_key not in self.token_timestamps and detected_time is not None:
                        self.token_timestamps[token_key] = detected_time

                    # --- HARD DROP FILTER: discard tokens below min_price_hard_drop ---
                    min_price_hard_drop = self.filters_cfg.get("min_price_hard_drop")
                    if min_price_hard_drop is not None:
                        try:
                            min_price_hard_drop_val = float(min_price_hard_drop)
                        except Exception:
                            min_price_hard_drop_val = None
                        if (
                            min_price_hard_drop_val is not None
                            and hasattr(token_info, 'price')
                            and token_info.price is not None
                            and token_info.price < min_price_hard_drop_val
                        ):
                            logger.info(f"[HARD DROP] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} < min_price_hard_drop={min_price_hard_drop_val}, discarding immediately.")
                            self.audit_logger.info(json.dumps({
                                "event": "hard_drop",
                                **token_info.to_dict(),
                                "price": getattr(token_info, 'price', None),
                                "min_price_hard_drop": min_price_hard_drop_val,
                                "time": datetime.utcnow().isoformat()
                            }))
                            continue

                    # --- MAX PRICE FILTER: discard tokens above max_price ---
                    max_price = self.filters_cfg.get("max_price")
                    if max_price is not None:
                        try:
                            max_price_val = float(max_price)
                        except Exception:
                            max_price_val = None
                        if (
                            max_price_val is not None
                            and hasattr(token_info, 'price')
                            and token_info.price is not None
                            and token_info.price > max_price_val
                        ):
                            logger.info(f"[MAX PRICE DROP] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} > max_price={max_price_val}, discarding immediately.")
                            self.audit_logger.info(json.dumps({
                                "event": "max_price_drop",
                                **token_info.to_dict(),
                                "price": getattr(token_info, 'price', None),
                                "max_price": max_price_val,
                                "time": datetime.utcnow().isoformat()
                            }))
                            continue
                    # --- ФИЛЬТРЫ ДЛЯ ПРОВЕРОК В ОТСТОЙНИКЕ ---
                    min_holders = self.filters_cfg.get("min_holders")
                    min_price = self.filters_cfg.get("min_price")
                    max_price = self.filters_cfg.get("max_price")
                    price_ok = True
                    holders_ok = True
                    # Новый порядок: если цена и holders подходят — покупаем сразу на любом recheck
                    if quarantine_type in ("price", "holders"):
                        recheck_delay = self.second_recheck_delay
                        max_recheck_attempts = self.second_max_recheck_attempts
                    else:
                        recheck_delay = self.recheck_delay
                        max_recheck_attempts = self.max_recheck_attempts
                    logger.info(f"[QUARANTINE] Token {token_info.symbol} (mint: {token_info.mint}) quarantine attempt {attempts+1}/{max_recheck_attempts} (type={quarantine_type}), recheck_delay={recheck_delay}s")
                    # Проверка цены
                    if min_price is not None:
                        logger.info(f"[PRICE INFO] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)} (recheck {attempts+1})")
                        logger.info(f"[PRICE DEBUG] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)}, min_price={min_price}")

                        # Добавляем цену в историю для анализа тренда
                        if not hasattr(token_info, 'price_history') or token_info.price_history is None:
                            token_info.price_history = []

                        if hasattr(token_info, 'price') and token_info.price is not None:
                            token_info.price_history.append(token_info.price)
                            # Оставляем ВСЮ ИСТОРИЮ quarantine (до 15 проверок за 15 секунд)
                            # Это позволит анализировать полный тренд, а не только последние 3-4 секунды
                            if len(token_info.price_history) > 15:
                                token_info.price_history = token_info.price_history[-15:]

                        price_ok = hasattr(token_info, 'price') and token_info.price is not None and token_info.price >= min_price

                        # Анализ тренда: покупаем только если цена растет
                        trend_ok = self._is_price_trending_up(token_info)
                        if price_ok and not trend_ok:
                            logger.info(f"[TREND BLOCK] Token {token_info.symbol} цена подходит ({token_info.price:.2e}), но тренд падающий - НЕ покупаем")
                            price_ok = False
                    if max_price is not None:
                        logger.info(f"[PRICE DEBUG] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)}, max_price={max_price}")
                        price_ok = price_ok and hasattr(token_info, 'price') and token_info.price is not None and token_info.price <= max_price
                    # Проверка holders
                    if min_holders is not None:
                        holders_ok = hasattr(token_info, 'holders') and token_info.holders >= min_holders
                    # Если оба фильтра проходят — покупаем сразу
                    if price_ok and holders_ok:
                        # Жёсткая проверка min_price перед instant buy
                        min_price = self.filters_cfg.get("min_price")
                        if min_price is not None:
                            try:
                                min_price_val = float(min_price)
                            except Exception:
                                min_price_val = None
                            if (
                                min_price_val is not None
                                and hasattr(token_info, 'price')
                                and token_info.price is not None
                                and token_info.price < min_price_val
                            ):
                                logger.info(f"[MIN PRICE BLOCK] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} < min_price={min_price_val}, discarding before instant buy!")
                                self.audit_logger.info(json.dumps({
                                    "event": "min_price_block",
                                    **token_info.to_dict(),
                                    "price": getattr(token_info, 'price', None),
                                    "min_price": min_price_val,
                                    "time": datetime.utcnow().isoformat()
                                }))
                                continue
                        # 🛡️ ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: тренд И стабильный рост
                        trend_ok = self._is_price_trending_up(token_info)
                        if not trend_ok:
                            logger.info(f"[QUARANTINE TREND BLOCK] Token {token_info.symbol} цена и holders подходят, но тренд падающий ({token_info.price:.2e}) - НЕ покупаем")
                            if self.token_queue.can_recheck(attempts+1, quarantine_type=quarantine_type):
                                self.token_queue.put_in_quarantine(token_info, attempts+1, detected_time=detected_time, quarantine_type=quarantine_type)
                            continue

                        if self._check_stable_growth(token_info):
                            logger.info(f"[QUARANTINE INSTANT BUY] Token {token_info.symbol} (mint: {token_info.mint}) price, holders, trend and stable growth ok, instant buy!")

                            # Выполняем покупку
                            try:
                                await self._handle_token(token_info, skip_filters=True)
                                # 📊 Логируем успешную покупку
                                self._log_purchase_attempt(token_info, "QUARANTINE INSTANT BUY", success=True)
                            except Exception as e:
                                # 📊 Логируем ошибку покупки
                                self._log_purchase_attempt(token_info, "QUARANTINE INSTANT BUY", success=False, error=str(e))
                                raise

                            self.processed_tokens.add(token_key)
                            self.token_timestamps.pop(token_key, None)
                            # Очищаем историю после покупки
                            self.price_history.pop(token_info.mint, None)
                            self.growth_checks.pop(token_info.mint, None)
                            continue
                        else:
                            # Не прошел проверку стабильного роста - продолжаем в quarantine
                            logger.info(f"[QUARANTINE] Token {token_info.symbol} не прошел проверку стабильного роста, остается в quarantine")
                            if self.token_queue.can_recheck(attempts+1, quarantine_type=quarantine_type):
                                self.token_queue.put_in_quarantine(token_info, attempts+1, detected_time=detected_time, quarantine_type=quarantine_type)
                            continue
                    # Если не проходит — стандартная логика отстойника
                    if not price_ok:
                        if self.token_queue.can_recheck(attempts+1, quarantine_type=quarantine_type):
                            self.token_queue.put_in_quarantine(token_info, attempts+1, detected_time=detected_time, quarantine_type=quarantine_type)
                            logger.info(f"Token {token_info.symbol} не прошёл фильтр цены, отправлен в отстойник для повторной проверки (quarantine_type={quarantine_type}, попытка {attempts+1})")
                            self.audit_logger.info(json.dumps({
                                "event": "quarantine",
                                **token_info.to_dict(),
                                "quarantine_type": quarantine_type,
                                "price": getattr(token_info, 'price', None),
                                "min_price": min_price,
                                "max_price": max_price,
                                "recheck_delay": recheck_delay,
                                "max_recheck_attempts": max_recheck_attempts,
                                "attempt": attempts+1,
                                "time": datetime.utcnow().isoformat()
                            }))
                        else:
                            logger.info(f"Token {token_info.symbol} не прошёл фильтр цены и превышено число попыток quarantine, отбрасывается")
                            self.audit_logger.info(json.dumps({
                                "event": "filter_failed",
                                **token_info.to_dict(),
                                "reason": "price",
                                "price": getattr(token_info, 'price', None),
                                "min_price": min_price,
                                "max_price": max_price,
                                "attempt": attempts+1,
                                "time": datetime.utcnow().isoformat()
                            }))
                        continue
                    if min_holders is not None and not holders_ok:
                        self.token_queue.put_in_quarantine(token_info, attempts+1, detected_time=detected_time, quarantine_type="holders")
                        logger.info(f"Token {token_info.symbol} не прошёл фильтр holders, отправлен во второй отстойник (quarantine_type=holders)")
                        self.audit_logger.info(json.dumps({
                            "event": "quarantine",
                            **token_info.to_dict(),
                            "quarantine_type": "holders",
                            "holders": getattr(token_info, 'holders', None),
                            "min_holders": min_holders,
                            "recheck_delay": recheck_delay,
                            "max_recheck_attempts": max_recheck_attempts,
                            "time": datetime.utcnow().isoformat()
                        }))
                        continue
                    # Если все фильтры пройдены — обычная логика
                    # === ЯВНОЕ ЛОГИРОВАНИЕ ВОЗРАСТА ПРИ ПОВТОРНОЙ ПРОВЕРКЕ ===
                    now_monotonic = monotonic()
                    saved_time_dbg = self.token_timestamps.get(token_key, detected_time)
                    age_dbg = now_monotonic - saved_time_dbg if saved_time_dbg is not None else None
                    logger.info(f"[AGE DEBUG] (quarantine_recheck) token_key={token_key}, now={now_monotonic}, saved={saved_time_dbg}, age={(age_dbg if age_dbg is not None else 'N/A'):.2f}s")
                    print(f"[AGE DEBUG PRINT] (quarantine_recheck) token_key={token_key}, now={now_monotonic}, saved={saved_time_dbg}, age={(age_dbg if age_dbg is not None else 'N/A'):.2f}s")
                    self.audit_logger.info(json.dumps({
                        "event": "quarantine_recheck_age_debug",
                        "token_key": token_key,
                        "now": now_monotonic,
                        "saved": saved_time_dbg,
                        "age": age_dbg,
                        "time": datetime.utcnow().isoformat()
                    }))
                    # === КОНЕЦ ЛОГИРОВАНИЯ ВОЗРАСТА ===
                    logger.info(f"Повторная проверка токена из отстойника: {token_info.symbol} (попытка {attempts+1})")
                    self.audit_logger.info(json.dumps({
                        "event": "quarantine_recheck",
                        **token_info.to_dict(),
                        "attempt": attempts+1,
                        "recheck_delay": recheck_delay,
                        "max_recheck_attempts": max_recheck_attempts,
                        "quarantine_type": quarantine_type,
                        "time": datetime.utcnow().isoformat()
                    }))
                    if self.token_filter.check(token_info):
                        logger.info(f"[QUARANTINE BUY] Token {token_info.symbol} (mint: {token_info.mint}) прошёл все фильтры в quarantine, инициируем покупку!")
                        await self._handle_token(token_info, skip_filters=True)
                        self.processed_tokens.add(token_key)
                        self.token_timestamps.pop(token_key, None)
                        continue
                    elif self.token_queue.can_recheck(attempts+1, quarantine_type=quarantine_type):
                        self.token_queue.put_in_quarantine(token_info, attempts+1, detected_time=detected_time, quarantine_type=quarantine_type)
                        logger.info(f"Токен {token_info.symbol} снова отправлен в отстойник (попытка {attempts+1}, quarantine_type={quarantine_type})")
                        self.audit_logger.info(json.dumps({
                            "event": "quarantine_repeat",
                            **token_info.to_dict(),
                            "attempt": attempts+1,
                            "recheck_delay": recheck_delay,
                            "max_recheck_attempts": max_recheck_attempts,
                            "quarantine_type": quarantine_type,
                            "time": datetime.utcnow().isoformat()
                        }))
                    else:
                        logger.info(f"Токен {token_info.symbol} окончательно отбрасывается после {attempts+1} попыток (quarantine_type={quarantine_type})")
                        self.audit_logger.info(json.dumps({
                            "event": "quarantine_drop",
                            **token_info.to_dict(),
                            "attempt": attempts+1,
                            "recheck_delay": recheck_delay,
                            "max_recheck_attempts": max_recheck_attempts,
                            "quarantine_type": quarantine_type,
                            "time": datetime.utcnow().isoformat()
                        }))
                        self.processed_tokens.add(token_key)
                        # Удаляем время первого обнаружения только теперь
                        self.token_timestamps.pop(token_key, None)
                        continue

                # Далее обычная очередь (неблокирующая проверка)
                try:
                    # Попытка получить токен без блокировки
                    priority, timestamp, token_info = self.token_queue._queue.get_nowait()
                    token_key = str(token_info.mint)
                except asyncio.QueueEmpty:
                    # Если нет новых токенов - короткая пауза для эффективной работы quarantine
                    await asyncio.sleep(0.1)  # 100ms пауза
                    continue
                # === FETCH PRICE ON FIRST APPEARANCE ===
                if token_key not in self.processed_tokens:
                    # Only fetch price if not already set
                    if not hasattr(token_info, 'price') or token_info.price is None:
                        try:
                            price = await self.curve_manager.calculate_price(token_info.bonding_curve)
                            if price is None:
                                logger.warning(f"[TOKEN MIGRATED] {token_info.symbol} (mint: {token_info.mint}) bonding curve not found - skipping")
                                continue
                            token_info.price = price
                            logger.info(f"[PRICE FETCHED] {token_info.symbol} (mint: {token_info.mint}) price={price} (first appearance)")
                        except Exception as e:
                            token_info.price = None
                            logger.warning(f"[PRICE ERROR] Failed to fetch price for {token_info.symbol} (mint: {token_info.mint}): {e!s}")

                    # --- HARD DROP FILTER: discard tokens below min_price_hard_drop ---
                    min_price_hard_drop = self.filters_cfg.get("min_price_hard_drop")
                    if min_price_hard_drop is not None:
                        try:
                            min_price_hard_drop_val = float(min_price_hard_drop)
                        except Exception:
                            min_price_hard_drop_val = None
                        if (
                            min_price_hard_drop_val is not None
                            and hasattr(token_info, 'price')
                            and token_info.price is not None
                            and token_info.price < min_price_hard_drop_val
                        ):
                            logger.info(f"[HARD DROP] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} < min_price_hard_drop={min_price_hard_drop_val}, discarding immediately.")
                            self.audit_logger.info(json.dumps({
                                "event": "hard_drop",
                                **token_info.to_dict(),
                                "price": getattr(token_info, 'price', None),
                                "min_price_hard_drop": min_price_hard_drop_val,
                                "time": datetime.utcnow().isoformat()
                            }))
                            continue

                    # --- MAX PRICE FILTER: discard tokens above max_price ---
                    max_price = self.filters_cfg.get("max_price")
                    if max_price is not None:
                        try:
                            max_price_val = float(max_price)
                        except Exception:
                            max_price_val = None
                        if (
                            max_price_val is not None
                            and hasattr(token_info, 'price')
                            and token_info.price is not None
                            and token_info.price > max_price_val
                        ):
                            logger.info(f"[MAX PRICE DROP] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} > max_price={max_price_val}, discarding immediately.")
                            self.audit_logger.info(json.dumps({
                                "event": "max_price_drop",
                                **token_info.to_dict(),
                                "price": getattr(token_info, 'price', None),
                                "max_price": max_price_val,
                                "time": datetime.utcnow().isoformat()
                            }))
                            continue

                    # ПРОВЕРЯЕМ ЦЕНУ СРАЗУ - ЕСЛИ ПОДХОДИТ, ПОКУПАЕМ БЕЗ QUARANTINE
                    min_price = self.filters_cfg.get("min_price")
                    max_price = self.filters_cfg.get("max_price")
                    price_ok = True

                    if min_price is not None and hasattr(token_info, 'price') and token_info.price is not None:
                        price_ok = token_info.price >= min_price
                    if max_price is not None and hasattr(token_info, 'price') and token_info.price is not None:
                        price_ok = price_ok and token_info.price <= max_price

                    if price_ok and hasattr(token_info, 'price') and token_info.price is not None:
                        # 🛡️ ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: тренд И стабильный рост (даже для INSTANT BUY)
                        trend_ok = self._is_price_trending_up(token_info)
                        if not trend_ok:
                            logger.info(f"[INSTANT BUY TREND BLOCK] Token {token_info.symbol} цена подходит ({token_info.price:.2e}), но тренд падающий - отправляем в quarantine")
                            self.token_queue.put_in_quarantine(token_info, 1, detected_time=self.token_timestamps.get(token_key))
                            continue

                        if self._check_stable_growth(token_info):
                            # ЦЕНА ПОДХОДИТ, ТРЕНД И РОСТ СТАБИЛЬНЫЙ - ПОКУПАЕМ СРАЗУ!
                            self.processed_tokens.add(token_key)
                            logger.info(f"[INSTANT BUY] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} подходит, тренд и рост стабильный - покупаем сразу!")

                            self.audit_logger.info(json.dumps({
                                "event": "instant_buy_price_ok",
                                **token_info.to_dict(),
                                "price": token_info.price,
                                "min_price": min_price,
                                "max_price": max_price,
                                "time": datetime.utcnow().isoformat()
                            }))

                            # Выполняем покупку
                            try:
                                await self._handle_token(token_info, skip_filters=True)
                                # 📊 Логируем успешную покупку
                                self._log_purchase_attempt(token_info, "INSTANT BUY", success=True)
                            except Exception as e:
                                # 📊 Логируем ошибку покупки
                                self._log_purchase_attempt(token_info, "INSTANT BUY", success=False, error=str(e))
                                raise

                            # Очищаем историю после покупки
                            self.price_history.pop(token_info.mint, None)
                            self.growth_checks.pop(token_info.mint, None)
                            continue
                        else:
                            # Цена подходит, но рост нестабильный - отправляем в quarantine
                            logger.info(f"[INSTANT BUY DELAYED] Token {token_info.symbol} цена подходит, но рост нестабильный - отправляем в quarantine")
                            self.token_queue.put_in_quarantine(token_info, 1, detected_time=self.token_timestamps.get(token_key))
                            continue
                    else:
                        # ЦЕНА НЕ ПОДХОДИТ - ОТПРАВЛЯЕМ В QUARANTINE
                        self.token_queue.put_in_quarantine(token_info, 1, detected_time=self.token_timestamps.get(token_key))
                        logger.info(f"Token {token_info.symbol} отправлен в отстойник для повторной проверки (quarantine_type=manual_quarantine_no_age)")
                        logger.info(f"[PRICE INFO] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)} (first appearance)")
                        self.audit_logger.info(json.dumps({
                            "event": "quarantine",
                            **token_info.to_dict(),
                            "quarantine_type": "manual_quarantine_no_age",
                            "recheck_delay": self.recheck_delay,
                            "max_recheck_attempts": self.max_recheck_attempts,
                            "price": getattr(token_info, 'price', None),
                            "time": datetime.utcnow().isoformat()
                        }))
                        continue

                # --- Новый порядок: holders -> price -> остальные фильтры ---
                min_holders = self.filters_cfg.get("min_holders")
                min_price = self.filters_cfg.get("min_price")
                max_price = self.filters_cfg.get("max_price")
                holders_ok = True
                price_ok = True
                # Проверка holders
                if min_holders is not None:
                    holders_ok = hasattr(token_info, 'holders') and token_info.holders >= min_holders
                if not holders_ok:
                    logger.info(f"Token {token_info.symbol} не прошёл фильтр holders (holders={getattr(token_info, 'holders', None)} < {min_holders}), отбрасывается")
                    self.audit_logger.info(json.dumps({
                        "event": "filter_failed",
                        **token_info.to_dict(),
                        "reason": "holders",
                        "holders": getattr(token_info, 'holders', None),
                        "min_holders": min_holders,
                        "time": datetime.utcnow().isoformat()
                    }))
                    continue
                # Проверка price
                if min_price is not None:
                    logger.info(f"[PRICE DEBUG] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)}, min_price={min_price}")
                    price_ok = hasattr(token_info, 'price') and token_info.price >= min_price
                if max_price is not None:
                    logger.info(f"[PRICE DEBUG] {token_info.symbol} (mint: {token_info.mint}) price={getattr(token_info, 'price', None)}, max_price={max_price}")
                    price_ok = price_ok and hasattr(token_info, 'price') and token_info.price <= max_price
                if not price_ok:
                    if self.recheck_on_price_fail and hasattr(token_info, 'price'):
                        self.token_queue.put_in_quarantine(token_info, 1)
                        logger.info(f"Token {token_info.symbol} не прошёл фильтр цены, но отправлен во второй отстойник для повторной проверки (quarantine_type=price)")
                        self.audit_logger.info(json.dumps({
                            "event": "quarantine",
                            **token_info.to_dict(),
                            "quarantine_type": "price",
                            "price": getattr(token_info, 'price', None),
                            "min_price": min_price,
                            "max_price": max_price,
                            "recheck_delay": self.recheck_delay,
                            "max_recheck_attempts": self.max_recheck_attempts,
                            "time": datetime.utcnow().isoformat()
                        }))
                        continue
                    logger.info(f"Token {token_info.symbol} не прошёл фильтр цены, отбрасывается")
                    self.audit_logger.info(json.dumps({
                        "event": "filter_failed",
                        **token_info.to_dict(),
                        "reason": "price",
                        "price": getattr(token_info, 'price', None),
                        "min_price": min_price,
                        "max_price": max_price,
                        "time": datetime.utcnow().isoformat()
                    }))
                    continue

                # После прохождения фильтра по holders и цене — сразу покупаем, остальные фильтры не проверяем
                # Жёсткая проверка min_price перед обычной покупкой из очереди
                min_price = self.filters_cfg.get("min_price")
                if min_price is not None:
                    try:
                        min_price_val = float(min_price)
                    except Exception:
                        min_price_val = None
                    if (
                        min_price_val is not None
                        and hasattr(token_info, 'price')
                        and token_info.price is not None
                        and token_info.price < min_price_val
                    ):
                        logger.info(f"[MIN PRICE BLOCK] Token {token_info.symbol} (mint: {token_info.mint}) price={token_info.price} < min_price={min_price_val}, discarding before main queue buy!")
                        self.audit_logger.info(json.dumps({
                            "event": "min_price_block",
                            **token_info.to_dict(),
                            "price": getattr(token_info, 'price', None),
                            "min_price": min_price_val,
                            "time": datetime.utcnow().isoformat()
                        }))
                        continue
                self.processed_tokens.add(token_key)
                logger.info(f"[INSTANT BUY] Token {token_info.symbol} (age: {token_age:.1f}s) прошёл фильтры holders и цены — инициируем покупку!")
                self.audit_logger.info(json.dumps({
                    "event": "passed_filters_instant_buy",
                    **token_info.to_dict(),
                    "age": round(token_age, 2),
                    "time": datetime.utcnow().isoformat()
                }))
                await self._handle_token(token_info, skip_filters=True)

                # Отмечаем задачу как выполненную после успешной обработки токена
                await self.token_queue.task_done()

            except asyncio.CancelledError:
                logger.info("Token queue processor was cancelled")
                break
            except Exception as e:
                logger.error(f"Error in token queue processor: {e!s}")
                # Если была ошибка при обработке токена из очереди, вызываем task_done()
                if 'token_info' in locals():
                    await self.token_queue.task_done()

    async def _handle_token(
        self, token_info: TokenInfo, skip_filters: bool = False
    ) -> None:
        """Handle a new token creation event.

        Args:
            token_info: Token information
            skip_filters: If True, skip filter checks (for INSTANT BUY tokens)
        """
        try:
            # Применяем фильтр перед обработкой токена (ТОЛЬКО если не INSTANT BUY)
            if not skip_filters and not self.token_filter.check(token_info):
                logger.info(f"Token {token_info.symbol} did not pass filters, skipping.")
                return

            # 🔥 ПРОВЕРКА MAX_ACTIVE_POSITIONS ПЕРЕД ПОКУПКОЙ
            if not await self.token_queue.can_start_new():
                logger.info(f"[MAX_ACTIVE_LIMIT] Cannot buy {token_info.symbol} - max active positions ({self.token_queue.max_active}) reached. Current active: {self.token_queue._active}")
                self.audit_logger.info(json.dumps({
                    "event": "max_active_limit_reached",
                    **token_info.to_dict(),
                    "max_active": self.token_queue.max_active,
                    "current_active": self.token_queue._active,
                    "time": datetime.utcnow().isoformat()
                }))
                return

            # Wait for bonding curve to stabilize
            if True:
                # Save token info to file
                # await self._save_token_info(token_info)
                logger.info(
                    f"Waiting for {self.wait_time_after_creation} seconds for the bonding curve to stabilize..."
                )
                await asyncio.sleep(self.wait_time_after_creation)

            # 🔥 НАЧИНАЕМ ТОРГОВЛЮ (увеличиваем счетчик активных позиций)
            await self.token_queue.start_trade()
            logger.info(f"[TRADE_START] Started trade for {token_info.symbol}. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

            # 🎯 Резервируем dedicated RPC для этой позиции
            if self.dedicated_rpc_manager:
                dedicated_client = self.dedicated_rpc_manager.reserve_rpc_for_position(token_info.symbol)
                if dedicated_client:
                    logger.info(f"🎯 Dedicated RPC зарезервирован для {token_info.symbol}")
                else:
                    logger.warning(f"⚠️ Не удалось зарезервировать RPC для {token_info.symbol}, используем общий")

            # Buy token
            logger.info(
                f"Buying {self.buy_amount:.6f} SOL worth of {token_info.symbol}..."
            )
            buy_result: TradeResult = await self.buyer.execute(token_info)

            if buy_result.success:
                await self._handle_successful_buy(token_info, buy_result)

            else:
                await self._handle_failed_buy(token_info, buy_result)

            # Only wait for next token in yolo mode
            if self.yolo_mode:
                logger.info(
                    f"YOLO mode enabled. Waiting {self.wait_time_before_new_token} seconds before looking for next token..."
                )
                await asyncio.sleep(self.wait_time_before_new_token)

        except Exception as e:
            logger.error(f"Error handling token {token_info.symbol}: {e!s}")
            # 🔥 ЗАВЕРШАЕМ ТОРГОВЛЮ при ошибке (уменьшаем счетчик активных позиций)
            await self.token_queue.finish_trade()
            logger.info(f"[TRADE_FINISH_ERROR] Finished trade for {token_info.symbol} due to error. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

    async def _handle_successful_buy(
        self, token_info: TokenInfo, buy_result: TradeResult
    ) -> None:
        """Handle successful token purchase.

        Args:
            token_info: Token information
            buy_result: The result of the buy operation
        """
        # ✅ ПРОСТАЯ ЛОГИКА КАК В РАБОЧЕМ БОТЕ - ДОВЕРЯЕМ buy_result
        # Рабочий бот не делает дополнительных проверок, просто доверяет buyer.execute()

        logger.info(f"Successfully bought {token_info.symbol}")
        self._log_trade(
            "buy",
            token_info,
            buy_result.price,  # type: ignore
            buy_result.amount,  # type: ignore
            buy_result.tx_signature,
        )
        self.audit_logger.info(json.dumps({
            "event": "buy",
            **token_info.to_dict(),
            "amount": buy_result.amount,
            "price": buy_result.price,
            "tx": str(buy_result.tx_signature),
            "strategy": self.exit_strategy_name
        }))
        self.traded_mints.add(token_info.mint)
        
        # Choose exit strategy
        if self.exit_strategy_name == "tp_sl":
            await self._handle_tp_sl_exit(token_info, buy_result)
        elif self.exit_strategy_name == "time_based":
            await self._handle_time_based_exit(token_info)
        elif self.exit_strategy_name == "manual":
            logger.info("Manual exit strategy - position will remain open")
            # 🔥 ЗАВЕРШАЕМ ТОРГОВЛЮ для manual режима (уменьшаем счетчик активных позиций)
            await self.token_queue.finish_trade()
            logger.info(f"[TRADE_FINISH_MANUAL] Finished manual trade for {token_info.symbol}. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

    async def _handle_failed_buy(
        self, token_info: TokenInfo, buy_result: TradeResult
    ) -> None:
        """Handle failed token purchase.

        Args:
            token_info: Token information
            buy_result: The result of the buy operation
        """
        logger.error(
            f"Failed to buy {token_info.symbol}: {buy_result.error_message}"
        )
        self.audit_logger.info(json.dumps({
            "event": "buy_error",
            **token_info.to_dict(),
            "error": buy_result.error_message
        }))

        # 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Освобождаем RPC если он был зарезервирован для неудачной покупки
        if self.dedicated_rpc_manager and buy_result.used_rpc_client:
            # Проверяем, был ли этот RPC зарезервирован как "занятый" (fake_index=999)
            for position_id, (rpc_index, client) in list(self.dedicated_rpc_manager.reserved_rpcs.items()):
                if client == buy_result.used_rpc_client and rpc_index == 999:
                    # Освобождаем фантомную резервацию
                    released = self.dedicated_rpc_manager.release_rpc(position_id)
                    if released:
                        logger.info(f"🔄 Освобожден RPC от неудачной покупки {token_info.symbol} (позиция {position_id})")
                    break

        # Close ATA if enabled
        await handle_cleanup_after_failure(
            self.solana_client,
            self.wallet,
            token_info.mint,
            self.priority_fee_manager,
            self.cleanup_mode,
            self.cleanup_with_priority_fee,
            self.cleanup_force_close_with_burn
        )
        # 🔥 ЗАВЕРШАЕМ ТОРГОВЛЮ после неудачной покупки (уменьшаем счетчик активных позиций)
        await self.token_queue.finish_trade()
        logger.info(f"[TRADE_FINISH_FAILED] Finished failed trade for {token_info.symbol}. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

    async def _handle_tp_sl_exit(self, token_info: TokenInfo, buy_result: TradeResult) -> None:
        """Handle take profit/stop loss exit strategy.

        Args:
            token_info: Token information
            buy_result: Result from the buy operation
        """
        # 🎯 ИСПОЛЬЗУЕМ RPC ИЗ ПОКУПКИ КАК DEDICATED ДЛЯ ЭТОЙ ПОЗИЦИИ
        dedicated_client = None
        position_id = f"pos_{token_info.symbol}_{int(monotonic() * 1000)}"

        # Получаем RPC который использовался для покупки
        buy_rpc_client = buy_result.used_rpc_client

        if self.dedicated_rpc_manager and buy_rpc_client:
            # Резервируем КОНКРЕТНЫЙ RPC который сделал покупку
            success = self.dedicated_rpc_manager.reserve_specific_rpc_for_position(position_id, buy_rpc_client)
            if success:
                dedicated_client = buy_rpc_client
                logger.info(f"🎯 RPC из покупки зарезервирован как dedicated для позиции {position_id}")
            else:
                logger.warning(f"⚠️ Не удалось зарезервировать RPC из покупки, пробуем резервировать любой доступный")
                # Пробуем зарезервировать любой доступный RPC
                dedicated_client = self.dedicated_rpc_manager.reserve_rpc_for_position(position_id)
                if dedicated_client:
                    logger.info(f"🎯 Зарезервирован fallback RPC для позиции {position_id}")
                else:
                    logger.warning(f"⚠️ Нет доступных RPC для резервирования, используем buy_rpc_client напрямую")
                    dedicated_client = buy_rpc_client  # Используем RPC из покупки напрямую
        else:
            logger.warning(f"⚠️ Нет dedicated_rpc_manager или buy_rpc_client")
            if self.dedicated_rpc_manager:
                # Пробуем зарезервировать любой доступный RPC
                dedicated_client = self.dedicated_rpc_manager.reserve_rpc_for_position(position_id)
                if dedicated_client:
                    logger.info(f"🎯 Зарезервирован RPC для позиции {position_id}")
                else:
                    logger.warning(f"⚠️ Нет доступных RPC, используем single RPC fallback")
                    # ИСПРАВЛЕНО: Используем один RPC вместо Multi-RPC Pool
                    if self.multi_rpc_provider:
                        # Берем первый доступный RPC как fallback
                        fallback_endpoint = self.multi_rpc_provider.endpoints[0]['url']
                        from src.core.client import SolanaClient
                        dedicated_client = SolanaClient(fallback_endpoint)
                        logger.info(f"🔄 Fallback RPC: {fallback_endpoint}")
                    else:
                        dedicated_client = self.solana_client
            else:
                dedicated_client = self.solana_client

        # 🔧 ИСПРАВЛЕНО: НЕ ДЕЛАЕМ ДОПОЛНИТЕЛЬНУЮ ПРОВЕРКУ ТРАНЗАКЦИИ
        # Если buyer.execute() вернул success=True, значит покупка прошла успешно
        # Дополнительная проверка через другой RPC может дать ложные ошибки
        logger.info(f"✅ Покупка {token_info.symbol} успешна, создаем позицию: {buy_result.amount:.6f} токенов")

        # Доверяем результату buyer.execute() - он уже проверил транзакцию
        actual_balance_decimal = buy_result.amount

        # Обновляем количество токенов в buy_result
        buy_result.amount = actual_balance_decimal

        # Create position - use ActivityBasedPosition if activity config is available
        if self.hold_time_config and self.hold_time_config.get("activity_based"):
            position = ActivityBasedPosition.create_from_buy_result(
                mint=token_info.mint,
                symbol=token_info.symbol,
                entry_price=buy_result.price,  # type: ignore
                quantity=buy_result.amount,    # type: ignore
                take_profit_percentage=self.take_profit_percentage,
                stop_loss_percentage=self.stop_loss_percentage,
                pre_trailing_stop_loss=self.pre_trailing_stop_loss,
                activity_config=self.hold_time_config,
                trailing_stop_percentage=self.trailing_stop_percentage,
                trailing_stop_activation=self.trailing_stop_activation,
            )
        else:
            # Legacy position with max_hold_time
            position = Position.create_from_buy_result(
                mint=token_info.mint,
                symbol=token_info.symbol,
                entry_price=buy_result.price,  # type: ignore
                quantity=buy_result.amount,    # type: ignore
                take_profit_percentage=self.take_profit_percentage,
                stop_loss_percentage=self.stop_loss_percentage,
                pre_trailing_stop_loss=self.pre_trailing_stop_loss,
                max_hold_time=self.max_hold_time,
                trailing_stop_percentage=self.trailing_stop_percentage,
                trailing_stop_activation=self.trailing_stop_activation,
            )
        
        # 🎯 Устанавливаем dedicated RPC для позиции
        if self.dedicated_rpc_manager and dedicated_client != self.solana_client:
            position.set_dedicated_rpc(position_id)
            position.position_id = position_id  # Обновляем ID позиции
            logger.info(f"🎯 Позиция {position_id} привязана к dedicated RPC")

        logger.info(f"Created position: {position}")
        if position.take_profit_price is not None:
            logger.info(f"Take profit target: {position.take_profit_price:.2e} SOL")
        if position.stop_loss_price is not None:
            logger.info(f"Stop loss target: {position.stop_loss_price:.2e} SOL")

        # Monitor position until exit condition is met
        await self._monitor_position_until_exit(token_info, position, dedicated_client)

    async def _handle_time_based_exit(self, token_info: TokenInfo) -> None:
        """Handle legacy time-based exit strategy.

        Args:
            token_info: Token information
        """
        try:
            logger.info(
                f"Waiting for {self.wait_time_after_buy} seconds before selling..."
            )
            await asyncio.sleep(self.wait_time_after_buy)

            logger.info(f"Selling {token_info.symbol}...")
            sell_result: TradeResult = await self.seller.execute(token_info)

            if sell_result.success:
                logger.info(f"Successfully sold {token_info.symbol}")
                self._log_trade(
                    "sell",
                    token_info,
                    sell_result.price,  # type: ignore
                    sell_result.amount,  # type: ignore
                    sell_result.tx_signature,
                )
                self.audit_logger.info(json.dumps({
                    "event": "sell",
                    **token_info.to_dict(),
                    "amount": sell_result.amount,
                    "price": sell_result.price,
                    "tx": str(sell_result.tx_signature),
                    "strategy": self.exit_strategy_name
                }))
                # Close ATA if enabled
                await handle_cleanup_after_sell(
                    self.solana_client,
                    self.wallet,
                    token_info.mint,
                    self.priority_fee_manager,
                    self.cleanup_mode,
                    self.cleanup_with_priority_fee,
                    self.cleanup_force_close_with_burn
                )
            else:
                logger.error(
                    f"🚨 КРИТИЧЕСКАЯ ОШИБКА: Failed to sell {token_info.symbol}: {sell_result.error_message}"
                )
                logger.warning(f"⚠️ Time-based exit: Позиция {token_info.symbol} НЕ будет закрыта - токены могут остаться в кошельке!")
                self.audit_logger.info(json.dumps({
                    "event": "sell_error",
                    **token_info.to_dict(),
                    "error": sell_result.error_message
                }))
        finally:
            # 🔥 ЗАВЕРШАЕМ ТОРГОВЛЮ после time-based exit (уменьшаем счетчик активных позиций)
            await self.token_queue.finish_trade()
            logger.info(f"[TRADE_FINISH_TIME] Finished time-based trade for {token_info.symbol}. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

    async def _monitor_position_until_exit(self, token_info, position, dedicated_client=None):
        """Monitor a position until exit conditions are met using selected exit strategy.

        Args:
            token_info: Token information
            position: Position to monitor
            dedicated_client: Optional dedicated SolanaClient for this position
        """
        logger.info(f"Monitoring position {position.symbol} with exit strategy: {self.exit_strategy_name}")

        try:
            while position.is_active:
                # 🎯 ИСПРАВЛЕНО: Цена проверяется через ВСЕ RPC для точности
                # Dedicated RPC используется ТОЛЬКО для продажи, НЕ для проверки цены
                current_price = await self._get_current_price(token_info)

                # Источник данных о цене (только debug уровень)
                # logger.debug(f"[PRICE SOURCE] {token_info.symbol} цена получена через MultiRPC")

                # 🔧 УМНОЕ ЛОГИРОВАНИЕ: только при значительных изменениях или ошибках
                if current_price is not None and current_price > 0:
                    # Проверяем, нужно ли логировать (каждые 10 итераций или при изменении >1%)
                    should_log_price = False

                    # Инициализируем счетчик итераций для позиции
                    if not hasattr(position, '_log_counter'):
                        position._log_counter = 0
                        position._last_logged_price = None

                    position._log_counter += 1

                    # Логируем каждые 25 итераций (25 * 0.4с = 10 секунд) или при значительном изменении цены
                    if (position._log_counter % 25 == 0 or
                        position._last_logged_price is None or
                        abs(current_price - position._last_logged_price) / position._last_logged_price > 0.01):  # >1% изменение
                        should_log_price = True
                        position._last_logged_price = current_price

                    if should_log_price:
                        # 🎯 ОПТИМИЗИРОВАНО: Минимальное логирование мониторинга
                        monitor_logger.debug(f"[MONITOR] {position.symbol} {current_price:.2e} SOL")

                        # Логируем только критические события trailing stop
                        if position.trailing_stop_percentage is not None:
                            max_price = getattr(position, '_max_price', position.entry_price)
                            trailing_activated = getattr(position, '_trailing_activated', False)

                            # Логируем только активацию trailing stop (один раз)
                            if (position.entry_price is not None and position.entry_price > 0 and
                                not trailing_activated and max_price is not None):
                                profit_pct = ((current_price - position.entry_price) / position.entry_price) * 100
                                activation_threshold = position.trailing_stop_activation or 0.15

                                if profit_pct >= activation_threshold * 100:
                                    logger.info(f"🎯 TRAILING ACTIVATED: {position.symbol} at {profit_pct:.1f}% profit")

                            # Логируем только когда trailing stop близко к срабатыванию
                            elif trailing_activated and max_price is not None:
                                trailing_stop_price = max_price * (1 - position.trailing_stop_percentage)
                                if current_price <= trailing_stop_price * 1.02:  # В пределах 2% от срабатывания
                                    logger.warning(f"⚠️ TRAILING CLOSE: {position.symbol} price={current_price:.2e}, stop={trailing_stop_price:.2e}")
                else:
                    # Логируем ошибки получения цены только на уровне debug
                    monitor_logger.debug(f"[MONITOR] {position.symbol} price fetch failed, using fallback")

                # Use activity-based exit logic if available
                if isinstance(position, ActivityBasedPosition):
                    should_exit, exit_reason = position.should_exit_activity(current_price)
                else:
                    should_exit, exit_reason = self.exit_strategy_impl.should_exit(position, current_price)

                if should_exit:
                    logger.info(f"Exit triggered for {position.symbol}: {exit_reason}")
                    # Безопасное логирование с проверкой None
                    exit_price_log = current_price if current_price is not None else "unknown"
                    self.audit_logger.info(json.dumps({
                        "event": "exit",
                        "symbol": position.symbol,
                        "mint": str(position.mint),
                        "exit_reason": str(exit_reason),
                        "exit_price": exit_price_log,
                        "strategy": self.exit_strategy_name
                    }))

                    # 🔥 ФАКТИЧЕСКАЯ ПРОДАЖА ТОКЕНА (через dedicated RPC)
                    logger.info(f"Selling {token_info.symbol} due to {exit_reason}...")

                    # Используем dedicated client если доступен
                    client_for_sell = dedicated_client if dedicated_client else self.solana_client
                    sell_result: TradeResult = await self.seller.execute(token_info, custom_client=client_for_sell)

                    if sell_result.success:
                        logger.info(f"Successfully sold {token_info.symbol}")
                        self._log_trade(
                            "sell",
                            token_info,
                            sell_result.price,  # type: ignore
                            sell_result.amount,  # type: ignore
                            sell_result.tx_signature,
                        )
                        self.audit_logger.info(json.dumps({
                            "event": "sell",
                            **token_info.to_dict(),
                            "amount": sell_result.amount,
                            "price": sell_result.price,
                            "tx": str(sell_result.tx_signature),
                            "strategy": self.exit_strategy_name,
                            "exit_reason": str(exit_reason)
                        }))
                        # Close ATA if enabled
                        await handle_cleanup_after_sell(
                            self.solana_client,
                            self.wallet,
                            token_info.mint,
                            self.priority_fee_manager,
                            self.cleanup_mode,
                            self.cleanup_with_priority_fee,
                            self.cleanup_force_close_with_burn
                        )
                    else:
                        # 🔧 ИСПРАВЛЕНО: Специальная обработка ошибки "Token account not ready"
                        if "Token account not ready" in str(sell_result.error_message):
                            logger.warning(f"⏰ Token account not ready for {token_info.symbol} - RPC синхронизация в процессе")
                            logger.warning(f"🔄 ПРОДОЛЖАЕМ МОНИТОРИНГ: Позиция {token_info.symbol} остается активной для повторных попыток продажи")
                        else:
                            logger.error(f"🚨 КРИТИЧЕСКАЯ ОШИБКА: Failed to sell {token_info.symbol}: {sell_result.error_message}")
                            logger.warning(f"🔄 ПРОДОЛЖАЕМ МОНИТОРИНГ: Позиция {token_info.symbol} остается активной для повторных попыток продажи")

                        self.audit_logger.info(json.dumps({
                            "event": "sell_error",
                            **token_info.to_dict(),
                            "error": sell_result.error_message,
                            "exit_reason": str(exit_reason)
                        }))

                        # 🚨 НЕ ЗАКРЫВАЕМ ПОЗИЦИЮ ПРИ НЕУДАЧНОЙ ПРОДАЖЕ!
                        # Позиция должна остаться активной для повторных попыток
                        logger.warning(f"⚠️ Позиция {token_info.symbol} НЕ закрыта - будем пытаться продать снова")

                        # Добавляем счетчик неудачных попыток
                        if not hasattr(position, 'failed_sell_attempts'):
                            position.failed_sell_attempts = 0
                            position.first_sell_attempt_time = time.time()
                        position.failed_sell_attempts += 1

                        logger.warning(f"🔄 Попытка продажи #{position.failed_sell_attempts} для {token_info.symbol}")

                        # 🚨 ПРОВЕРКА ЛИМИТОВ ПОПЫТОК ПРОДАЖИ
                        max_sell_attempts = getattr(self, 'max_sell_attempts', 10)  # Из конфигурации
                        abandon_after_minutes = getattr(self, 'abandon_after_minutes', 5)  # Из конфигурации

                        # Проверка по количеству попыток
                        if position.failed_sell_attempts >= max_sell_attempts:
                            logger.error(f"🚨 ПРЕВЫШЕН ЛИМИТ ПОПЫТОК: {position.failed_sell_attempts}/{max_sell_attempts} для {token_info.symbol}")
                            logger.error(f"🚨 ПРИНУДИТЕЛЬНОЕ ЗАКРЫТИЕ ПОЗИЦИИ - токен остается в кошельке!")
                            position.close_position(current_price, f"MAX_SELL_ATTEMPTS_EXCEEDED_{max_sell_attempts}")
                            break

                        # Проверка по времени
                        elapsed_minutes = (time.time() - position.first_sell_attempt_time) / 60
                        if elapsed_minutes >= abandon_after_minutes:
                            logger.error(f"🚨 ПРЕВЫШЕНО ВРЕМЯ ПОПЫТОК: {elapsed_minutes:.1f}/{abandon_after_minutes} мин для {token_info.symbol}")
                            logger.error(f"🚨 ПРИНУДИТЕЛЬНОЕ ЗАКРЫТИЕ ПОЗИЦИИ - токен остается в кошельке!")
                            position.close_position(current_price, f"SELL_TIMEOUT_{abandon_after_minutes}min")
                            break

                        # 🔧 ИСПРАВЛЕНО: Умные повторные попытки с учетом типа ошибки
                        if "Token account not ready" in str(sell_result.error_message):
                            # Для ошибок синхронизации - быстрый retry
                            wait_time = min(1.0 * position.failed_sell_attempts, 5)  # 1-5 сек
                            logger.warning(f"⏰ Быстрый повтор #{position.failed_sell_attempts} через {wait_time:.1f} сек (account sync)...")
                        else:
                            # Для других ошибок - обычная задержка
                            sell_retry_delay = getattr(self, 'sell_retry_delay', 2.0)  # Из конфигурации
                            wait_time = min(sell_retry_delay * position.failed_sell_attempts, 10)  # Макс 10 сек
                            logger.warning(f"⏰ Повтор #{position.failed_sell_attempts} через {wait_time:.1f} сек...")

                        await asyncio.sleep(wait_time)
                        continue  # Продолжаем мониторинг вместо break

                    # Закрываем позицию ТОЛЬКО при успешной продаже
                    position.close_position(current_price, exit_reason)
                    break

                # ВСЕГДА используем price_check_interval для мониторинга цены (0.4 сек)
                sleep_interval = self.price_check_interval

                # 🔧 УМНОЕ ЛОГИРОВАНИЕ АКТИВНОСТИ: только при изменении уровня или каждые 60 секунд
                if isinstance(position, ActivityBasedPosition):
                    activity_level = position.get_activity_level()
                    next_activity_check = position.get_next_check_interval()

                    # Инициализируем отслеживание активности
                    if not hasattr(position, '_last_activity_level'):
                        position._last_activity_level = None
                        position._activity_log_counter = 0

                    position._activity_log_counter += 1

                    # Логируем при изменении уровня активности или каждые 150 итераций (60 секунд)
                    if (activity_level != position._last_activity_level or
                        position._activity_log_counter % 150 == 0):
                        logger.info(f"[ACTIVITY] {position.symbol} level: {activity_level}, next activity check in {next_activity_check}s")
                        position._last_activity_level = activity_level

                await asyncio.sleep(sleep_interval)
        finally:
            # 🎯 ОСВОБОЖДАЕМ DEDICATED RPC
            if self.dedicated_rpc_manager and hasattr(position, 'position_id') and position.position_id:
                # Проверяем, есть ли зарезервированный RPC для этой позиции
                if position.position_id in self.dedicated_rpc_manager.reserved_rpcs:
                    released = self.dedicated_rpc_manager.release_rpc(position.position_id)
                    if released:
                        logger.info(f"🔄 Dedicated RPC освобожден для позиции {position.position_id}")
                    else:
                        logger.warning(f"⚠️ Не удалось освободить RPC для позиции {position.position_id}")
                else:
                    logger.info(f"ℹ️ Позиция {position.position_id} не имела зарезервированного RPC")

            # 🔥 ЗАВЕРШАЕМ ТОРГОВЛЮ после продажи (уменьшаем счетчик активных позиций)
            await self.token_queue.finish_trade()
            logger.info(f"[TRADE_FINISH_TP_SL] Finished trade for {position.symbol}. Active positions: {self.token_queue._active}/{self.token_queue.max_active}")

    async def _get_current_price(self, token_info: TokenInfo) -> float:
        """Get current price of a token from bonding curve.

        Args:
            token_info: Token information containing bonding curve address

        Returns:
            Current token price in SOL
        """
        try:
            price = await self.curve_manager.calculate_price(token_info.bonding_curve)
            if price is not None and price > 0:
                return price
            else:
                logger.warning(f"Invalid price received for {token_info.symbol}: {price}")
                return getattr(token_info, 'price', 0.0)
        except Exception as e:
            logger.error(f"Failed to get current price for {token_info.symbol}: {e!s}")
            # Return a fallback price (entry price if available, or small positive value)
            fallback_price = getattr(token_info, 'price', 1e-9)
            return max(fallback_price, 1e-9)  # Ensure positive value

    async def _get_current_price_with_client(self, token_info: TokenInfo, client: SolanaClient) -> float:
        """Get current price of a token using specific client (e.g., dedicated RPC).

        Args:
            token_info: Token information containing bonding curve address
            client: Specific SolanaClient to use for the request

        Returns:
            Current token price in SOL
        """
        try:
            # Создаем временный curve manager с dedicated client
            from src.core.curve import BondingCurveManager
            dedicated_curve_manager = BondingCurveManager(client)
            price = await dedicated_curve_manager.calculate_price(token_info.bonding_curve)
            if price is not None and price > 0:
                return price
            else:
                logger.warning(f"Invalid price received for {token_info.symbol} via dedicated RPC: {price}")
                return getattr(token_info, 'price', 1e-9)
        except Exception as e:
            logger.warning(f"Failed to get current price for {token_info.symbol} via dedicated RPC: {e}")
            # ИСПРАВЛЕНО: Fallback на один RPC вместо Multi-RPC Pool
            try:
                if self.multi_rpc_provider:
                    # Берем первый доступный RPC как fallback
                    fallback_endpoint = self.multi_rpc_provider.endpoints[0]['url']
                    from src.core.client import SolanaClient
                    fallback_client = SolanaClient(fallback_endpoint)
                    logger.info(f"🔄 Using fallback RPC for {token_info.symbol}: {fallback_endpoint}")

                    # Создаем curve manager с fallback client
                    from src.core.curve import BondingCurveManager
                    fallback_curve_manager = BondingCurveManager(fallback_client)
                    fallback_price = await fallback_curve_manager.calculate_price(token_info.bonding_curve)

                    if fallback_price is not None and fallback_price > 0:
                        return fallback_price

                # Если fallback тоже не сработал, возвращаем последнюю известную цену
                logger.warning(f"⚠️ All RPC failed for {token_info.symbol}, using last known price")
                return getattr(token_info, 'price', 1e-9)

            except Exception as fallback_error:
                logger.error(f"❌ Fallback RPC also failed for {token_info.symbol}: {fallback_error}")
                return getattr(token_info, 'price', 1e-9)

    async def _save_token_info(
        self, token_info: TokenInfo
    ) -> None:
        """Save token information to a file.

        Args:
            token_info: Token information
        """
        try:
            os.makedirs("trades", exist_ok=True)
            file_name = os.path.join("trades", f"{token_info.mint}.txt")

            with open(file_name, "w") as file:
                file.write(json.dumps(token_info.to_dict(), indent=2))

            logger.info(f"Token information saved to {file_name}")
        except Exception as e:
            logger.error(f"Failed to save token information: {e!s}")

    def _log_trade(
        self,
        action: str,
        token_info: TokenInfo,
        price: float,
        amount: float,
        tx_hash: str | None,
    ) -> None:
        """Log trade information.

        Args:
            action: Trade action (buy/sell)
            token_info: Token information
            price: Token price in SOL
            amount: Trade amount in SOL
            tx_hash: Transaction hash
        """
        try:
            os.makedirs("trades", exist_ok=True)

            log_entry = {
                "timestamp": datetime.utcnow().isoformat(),
                "action": action,
                "token_address": str(token_info.mint),
                "symbol": token_info.symbol,
                "price": price,
                "amount": amount,
                "tx_hash": str(tx_hash) if tx_hash else None,
            }

            with open("trades/trades.log", "a") as log_file:
                log_file.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            logger.error(f"Failed to log trade information: {e!s}")

    def _is_price_trending_up(self, token_info: TokenInfo) -> bool:
        """Анализирует тренд цены токена с учетом толерантности к малым падениям.

        ИСПРАВЛЕНО: Теперь анализирует ВЕСЬ период quarantine, а не только последние 3 цены!

        Args:
            token_info: Информация о токене с историей цен

        Returns:
            True если цена растет или падения в пределах толерантности, False если сильно падает
        """
        if not hasattr(token_info, 'price_history') or not token_info.price_history:
            logger.info(f"[TREND] {token_info.symbol} - недостаточно данных для анализа тренда")
            return True  # Если нет истории, разрешаем покупку

        history = token_info.price_history

        # Нужно минимум 2 цены для анализа тренда
        if len(history) < 2:
            logger.info(f"[TREND] {token_info.symbol} - мало данных ({len(history)} цен)")
            return True

        # Получаем порог толерантности из конфигурации (по умолчанию 5%)
        trend_tolerance = self.filters_cfg.get("trend_tolerance", 0.05) * 100  # Конвертируем в проценты

        # 🚀 НОВАЯ ЛОГИКА: Анализируем ОБЩИЙ тренд за весь период quarantine
        first_price = history[0]
        last_price = history[-1]
        overall_change = (last_price - first_price) / first_price * 100

        # Также проверяем последние 3 изменения для краткосрочного тренда
        recent_changes = []
        if len(history) >= 3:
            for i in range(-2, 0):  # Последние 2 изменения
                change = (history[i] - history[i-1]) / history[i-1] * 100
                recent_changes.append(change)
        elif len(history) >= 2:
            change = (history[-1] - history[-2]) / history[-2] * 100
            recent_changes.append(change)

        # Тренд считается восходящим если:
        # 1. Общий рост за период > -tolerance ИЛИ
        # 2. Последние изменения не превышают tolerance
        overall_trend_ok = overall_change > -trend_tolerance
        recent_trend_ok = all(change > -trend_tolerance for change in recent_changes) if recent_changes else True

        # Финальное решение: должен быть хотя бы один положительный сигнал
        trend_up = overall_trend_ok or recent_trend_ok

        # Детальное логирование
        if len(history) >= 3:
            logger.info(f"[TREND] {token_info.symbol} - цены: {[f'{p:.2e}' for p in history[-3:]]} | изменения: {', '.join([f'{c:+.1f}%' for c in recent_changes])} | общий: {overall_change:+.1f}% | толерантность: ±{trend_tolerance:.1f}% | тренд: {'UP' if trend_up else 'DOWN'}")
        else:
            logger.info(f"[TREND] {token_info.symbol} - цены: {[f'{p:.2e}' for p in history]} | общий: {overall_change:+.1f}% | толерантность: ±{trend_tolerance:.1f}% | тренд: {'UP' if trend_up else 'DOWN'}")

        return trend_up