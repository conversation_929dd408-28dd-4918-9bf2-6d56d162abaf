import asyncio
import heapq
import time
from typing import Any

class DelayedTokenQueue:
    def __init__(self):
        self._heap = []  # (ready_time, token)
        self._lock = asyncio.Lock()

    async def add(self, token: Any, delay: float):
        ready_time = time.time() + delay
        async with self._lock:
            heapq.heappush(self._heap, (ready_time, token))

    async def get_next_ready(self):
        while True:
            async with self._lock:
                if self._heap and self._heap[0][0] <= time.time():
                    _, token = heapq.heappop(self._heap)
                    return token
            await asyncio.sleep(0.1)  # онлайн-проверка очереди

    async def qsize(self):
        async with self._lock:
            return len(self._heap)
