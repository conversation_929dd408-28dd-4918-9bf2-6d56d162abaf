"""
Sell operations for pump.fun tokens.
SIMPLIFIED VERSION BASED ON WORKING BOT!
"""

import asyncio
import struct
from typing import Final

from solders.instruction import AccountMeta, Instruction
from solders.pubkey import Pubkey

from src.core.client import SolanaClient
from src.core.curve import BondingCurveManager
from src.core.priority_fee.manager import PriorityFeeManager
from src.core.pubkeys import (
    LAMPORTS_PER_SOL,
    TOKEN_DECIMALS,
    PumpAddresses,
    SystemAddresses,
)
from src.core.wallet import Wallet
from src.trading.base import TokenInfo, Trader, TradeResult
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Discriminator for the sell instruction
EXPECTED_DISCRIMINATOR: Final[bytes] = struct.pack("<Q", 12502976635542562355)


class TokenSeller(Trader):
    """Handles selling tokens on pump.fun."""

    def __init__(
        self,
        client: SolanaClient,
        wallet: Wallet,
        curve_manager: Bonding<PERSON><PERSON><PERSON><PERSON><PERSON>ger,
        priority_fee_manager: PriorityFee<PERSON>anager,
        slippage: float = 0.25,
        max_retries: int = 5,
        dry_run: bool = False,
    ):
        """Initialize token seller.

        Args:
            client: Solana client for RPC calls
            wallet: Wallet for signing transactions
            curve_manager: Bonding curve manager
            slippage: Slippage tolerance (0.25 = 25%)
            max_retries: Maximum number of retry attempts
        """
        self.client = client
        self.wallet = wallet
        self.curve_manager = curve_manager
        self.priority_fee_manager = priority_fee_manager
        self.slippage = slippage
        self.max_retries = max_retries
        self.dry_run = dry_run



    async def execute(self, token_info: TokenInfo, custom_client: SolanaClient = None, current_price: float = None) -> TradeResult:
        """Execute sell operation.

        Args:
            token_info: Token information
            custom_client: Optional dedicated client for this operation
            current_price: Current token price (optional)

        Returns:
            TradeResult with sell outcome
        """
        try:
            # 🎯 Используем dedicated client если предоставлен, иначе основной
            client_to_use = custom_client if custom_client is not None else self.client

            # Логируем какой RPC используется
            rpc_type = "DEDICATED" if custom_client is not None else "ОБЩИЙ"
            logger.info(f"💸 ПРОДАЖА {token_info.symbol} через {rpc_type} RPC")

            if custom_client:
                logger.debug(f"🎯 Using dedicated RPC for selling {token_info.symbol}")

            # Get associated token account
            associated_token_account = self.wallet.get_associated_token_address(
                token_info.mint
            )

            # 🔧 ИСПРАВЛЕНО: Правильная обработка "could not find account"
            try:
                # Get token balance
                token_balance = await client_to_use.get_token_account_balance(
                    associated_token_account
                )
                token_balance_decimal = token_balance / 10**TOKEN_DECIMALS

                logger.info(f"Token balance: {token_balance_decimal}")

                if token_balance == 0:
                    logger.info("No tokens to sell.")
                    return TradeResult(success=False, error_message="No tokens to sell", used_rpc_client=client_to_use)

            except ValueError as e:
                if "not found" in str(e).lower():
                    # Token account не создан или еще не синхронизирован
                    logger.warning(f"⚠️ Token account not found: {e}")
                    return TradeResult(success=False, error_message=f"Token account not ready: {e}", used_rpc_client=client_to_use)
                else:
                    raise

            # Fetch token price
            curve_state = await self.curve_manager.get_curve_state(
                token_info.bonding_curve
            )
            token_price_sol = curve_state.calculate_price()

            logger.info(f"Price per Token: {token_price_sol:.8f} SOL")

            # Calculate minimum SOL output with slippage
            amount = token_balance
            expected_sol_output = float(token_balance_decimal) * float(token_price_sol)
            slippage_factor = 1 - self.slippage
            min_sol_output = int(
                (expected_sol_output * slippage_factor) * LAMPORTS_PER_SOL
            )

            logger.info(f"Selling {token_balance_decimal} tokens")
            logger.info(f"Expected SOL output: {expected_sol_output:.8f} SOL")
            logger.info(
                f"Minimum SOL output (with {self.slippage * 100}% slippage): {min_sol_output / LAMPORTS_PER_SOL:.8f} SOL"
            )

            # 🧪 DRY RUN MODE - СИМУЛЯЦИЯ БЕЗ РЕАЛЬНОЙ ПРОДАЖИ
            if self.dry_run:
                logger.info("🧪 DRY RUN: Simulating sell transaction (no real sale)")

                # Симулируем успешную транзакцию
                import time
                mint_str = str(token_info.mint)
                fake_signature = f"DRY_RUN_{int(time.time())}_SELL_{mint_str[:8]}"

                return TradeResult(
                    success=True,
                    tx_signature=fake_signature,
                    amount=expected_sol_output / LAMPORTS_PER_SOL,  # SOL получено
                    price=current_price,
                    error_message=None,
                    used_rpc_client=client_to_use
                )

            tx_signature = await self._send_sell_transaction(
                token_info,
                associated_token_account,
                amount,
                min_sol_output,
                client_to_use,
            )

            success = await client_to_use.confirm_transaction(tx_signature)

            if success:
                logger.info(f"Sell transaction confirmed: {tx_signature}")

                # 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем РЕАЛЬНЫЙ результат продажи
                try:
                    # Проверяем, действительно ли токены были проданы через ТОТ ЖЕ RPC
                    actual_sol_received = await self._get_actual_sell_result(tx_signature, token_info, client_to_use)

                    if actual_sol_received <= 0:
                        error_msg = f"🚨 SELL EXECUTION FAILED: Transaction confirmed but no SOL received! Check slippage settings."
                        logger.error(error_msg)
                        return TradeResult(
                            success=False,
                            error_message=error_msg,
                            tx_signature=tx_signature,
                            used_rpc_client=client_to_use
                        )

                    logger.info(f"✅ SELL SUCCESS: Received {actual_sol_received:.6f} SOL")
                    return TradeResult(
                        success=True,
                        tx_signature=tx_signature,
                        amount=actual_sol_received,
                        price=token_price_sol,
                        used_rpc_client=client_to_use
                    )

                except Exception as e:
                    error_msg = f"🚨 SELL VERIFICATION FAILED: {e}"
                    logger.error(error_msg)
                    return TradeResult(
                        success=False,
                        error_message=error_msg,
                        tx_signature=tx_signature,
                        used_rpc_client=client_to_use
                    )
            else:
                return TradeResult(
                    success=False,
                    error_message=f"Transaction failed to confirm: {tx_signature}",
                    used_rpc_client=client_to_use
                )

        except Exception as e:
            logger.error(f"Sell operation failed: {e!s}")
            return TradeResult(success=False, error_message=str(e), used_rpc_client=client_to_use)

    async def _get_actual_sell_result(self, tx_signature: str, token_info: TokenInfo, client: SolanaClient) -> float:
        """Проверяет реальный результат продажи - сколько SOL было получено."""
        try:
            solana_client = await client.get_client()

            # Получаем детали транзакции
            tx_response = await solana_client.get_transaction(
                tx_signature,
                encoding="jsonParsed",
                commitment="confirmed",
                max_supported_transaction_version=0
            )

            if not tx_response.value:
                logger.warning(f"Transaction {tx_signature} not found")
                return 0.0

            # Получаем meta из правильного места
            meta = tx_response.value.meta if hasattr(tx_response.value, 'meta') else None

            # Проверяем, что транзакция успешна
            if meta and meta.err:
                logger.error(f"Transaction failed with error: {tx_response.value.meta.err}")
                return 0.0

            # Ищем изменения баланса SOL в нашем кошельке
            if meta and meta.pre_balances and meta.post_balances:
                # Первый аккаунт обычно наш кошелек
                pre_balance = meta.pre_balances[0]
                post_balance = meta.post_balances[0]

                # Учитываем комиссию транзакции
                fee = meta.fee if meta.fee else 0

                # Реальное изменение SOL (исключая комиссию)
                sol_change = (post_balance - pre_balance + fee) / 1_000_000_000  # Конвертируем в SOL

                if sol_change > 0:
                    logger.info(f"SOL received from sell: {sol_change:.6f} SOL")
                    return sol_change
                else:
                    logger.warning(f"No SOL received from sell. Balance change: {sol_change:.6f} SOL")
                    return 0.0

            logger.warning("Could not determine SOL received from transaction")
            return 0.0

        except Exception as e:
            logger.error(f"Failed to verify sell result: {e}")
            return 0.0

    async def _send_sell_transaction(
        self,
        token_info: TokenInfo,
        associated_token_account: Pubkey,
        amount: int,
        min_sol_output: int,
        client: SolanaClient,
    ) -> str:
        """Send sell transaction to the network.

        Args:
            token_info: Token information
            associated_token_account: Associated token account address
            amount: Amount of tokens to sell
            min_sol_output: Minimum SOL output expected
            client: Solana client to use

        Returns:
            Transaction signature
        """
        # Prepare accounts for the sell instruction
        accounts = [
            AccountMeta(PumpAddresses.GLOBAL, False, False),
            AccountMeta(PumpAddresses.FEE, False, True),
            AccountMeta(token_info.mint, False, False),
            AccountMeta(token_info.bonding_curve, False, True),
            AccountMeta(token_info.associated_bonding_curve, False, True),
            AccountMeta(associated_token_account, False, True),
            AccountMeta(self.wallet.pubkey, True, True),  # ✅ FIXED: wallet.pubkey instead of keypair.pubkey()
            AccountMeta(SystemAddresses.PROGRAM, False, False),
            AccountMeta(token_info.creator_vault, False, True),  # ✅ FIXED: Added creator_vault (CRITICAL!)
            AccountMeta(SystemAddresses.TOKEN_PROGRAM, False, False),
            AccountMeta(PumpAddresses.EVENT_AUTHORITY, False, False),
            AccountMeta(PumpAddresses.PROGRAM, False, False),
        ]

        # Prepare sell instruction data
        data = EXPECTED_DISCRIMINATOR + struct.pack("<Q", amount) + struct.pack("<Q", min_sol_output)
        sell_ix = Instruction(PumpAddresses.PROGRAM, data, accounts)

        try:
            return await client.build_and_send_transaction(
                [sell_ix],
                self.wallet.keypair,
                skip_preflight=True,
                max_retries=self.max_retries,
                priority_fee=await self.priority_fee_manager.calculate_priority_fee(
                    self._get_relevant_accounts(token_info)
                ),
            )
        except Exception as e:
            logger.error(f"Sell transaction failed: {e!s}")
            raise

    def _get_relevant_accounts(self, token_info: TokenInfo) -> list[Pubkey]:
        """Get relevant accounts for priority fee calculation.

        Args:
            token_info: Token information

        Returns:
            List of relevant account addresses
        """
        return [
            token_info.bonding_curve,
            token_info.associated_bonding_curve,
            self.wallet.get_associated_token_address(token_info.mint),
        ]
