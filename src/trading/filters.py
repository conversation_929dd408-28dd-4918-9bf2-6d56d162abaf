"""
Flexible token filter system for pump-fun-bot.
Each filter is a class with a .check(token_info, context) method.
Filters can be composed and configured via YAML.
"""
from typing import Any, Dict, List, Optional
from src.trading.base import TokenInfo

class BaseTokenFilter:
    def check(self, token_info: TokenInfo, context: Optional[Dict[str, Any]] = None) -> bool:
        """Return True if token passes the filter, False otherwise."""
        raise NotImplementedError

class NameContainsFilter(BaseTokenFilter):
    def __init__(self, substring: str):
        self.substring = substring.lower()
    def check(self, token_info: TokenInfo, context=None) -> bool:
        return self.substring in token_info.symbol.lower() or self.substring in token_info.name.lower()

class CreatorAddressFilter(BaseTokenFilter):
    def __init__(self, creator_address: str):
        self.creator_address = creator_address
    def check(self, token_info: TokenInfo, context=None) -> bool:
        return token_info.creator == self.creator_address

class MaxTokenAgeFilter(BaseTokenFilter):
    def __init__(self, max_age: float):
        self.max_age = max_age
    def check(self, token_info: TokenInfo, context=None) -> bool:
        return token_info.age_seconds <= self.max_age


class MinTokenAgeFilter(BaseTokenFilter):
    def __init__(self, min_age: float):
        self.min_age = min_age
    def check(self, token_info: TokenInfo, context=None) -> bool:
        return hasattr(token_info, 'age_seconds') and token_info.age_seconds >= self.min_age

class MinPriceFilter(BaseTokenFilter):
    def __init__(self, min_price: float):
        self.min_price = min_price
    def check(self, token_info: TokenInfo, context=None) -> bool:
        from utils.logger import get_logger
        logger = get_logger(__name__)
        logger.info(f"[FILTER][MinPrice] {getattr(token_info, 'symbol', '')} (mint: {getattr(token_info, 'mint', '')}) price={getattr(token_info, 'price', None)}, min_price={self.min_price}")
        return hasattr(token_info, 'price') and token_info.price >= self.min_price

class MaxPriceFilter(BaseTokenFilter):
    def __init__(self, max_price: float):
        self.max_price = max_price
    def check(self, token_info: TokenInfo, context=None) -> bool:
        from utils.logger import get_logger
        logger = get_logger(__name__)
        logger.info(f"[FILTER][MaxPrice] {getattr(token_info, 'symbol', '')} (mint: {getattr(token_info, 'mint', '')}) price={getattr(token_info, 'price', None)}, max_price={self.max_price}")
        return hasattr(token_info, 'price') and token_info.price <= self.max_price

class MinHoldersFilter(BaseTokenFilter):
    def __init__(self, min_holders: int):
        self.min_holders = min_holders
    def check(self, token_info: TokenInfo, context=None) -> bool:
        holders = getattr(token_info, 'holders', None)
        result = holders is not None and holders >= self.min_holders
        from src.utils.logger import get_logger
        logger = get_logger(__name__)
        logger.info(f"[FILTER][MinHolders] {getattr(token_info, 'symbol', '')} (mint: {getattr(token_info, 'mint', '')}) holders={holders}, min_holders={self.min_holders}, passed={result}")
        return result

class CompositeFilter(BaseTokenFilter):
    def __init__(self, filters: List[BaseTokenFilter]):
        self.filters = filters
    def check(self, token_info: TokenInfo, context=None) -> bool:
        return all(f.check(token_info, context) for f in self.filters)

# Factory to build filters from config
def build_filters_from_config(filters_cfg: dict) -> BaseTokenFilter:
    filters: List[BaseTokenFilter] = []
    if filters_cfg.get("match_string"):
        filters.append(NameContainsFilter(filters_cfg["match_string"]))
    if filters_cfg.get("bro_address"):
        filters.append(CreatorAddressFilter(filters_cfg["bro_address"]))
    if filters_cfg.get("min_token_age") is not None:
        filters.append(MinTokenAgeFilter(filters_cfg["min_token_age"]))
    if filters_cfg.get("max_token_age") is not None:
        filters.append(MaxTokenAgeFilter(filters_cfg["max_token_age"]))
    if filters_cfg.get("min_price") is not None:
        filters.append(MinPriceFilter(filters_cfg["min_price"]))
    if filters_cfg.get("max_price") is not None:
        filters.append(MaxPriceFilter(filters_cfg["max_price"]))
    if filters_cfg.get("min_holders") is not None:
        filters.append(MinHoldersFilter(filters_cfg["min_holders"]))
    # min_liquidity, etc. — можно добавить аналогично, если появится в TokenInfo
    if not filters:
        # Если фильтры отключены - создаем фильтр, который ВСЕГДА ПРОПУСКАЕТ токены
        class AlwaysPassFilter(BaseTokenFilter):
            def check(self, token_info, context=None) -> bool:
                return True
        return AlwaysPassFilter()
    if len(filters) == 1:
        return filters[0]
    return CompositeFilter(filters)
