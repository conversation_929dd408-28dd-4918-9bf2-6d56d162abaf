"""
Buy operations for pump.fun tokens.
"""

import struct
from typing import Final

from solders.instruction import AccountMeta, Instruction
from solders.pubkey import Pubkey
from spl.token.instructions import create_idempotent_associated_token_account

from src.core.client import SolanaClient
from src.core.curve import BondingCurveManager
from src.core.priority_fee.manager import PriorityFeeManager
from src.core.pubkeys import (
    LAMPORTS_PER_SOL,
    TOKEN_DECIMALS,
    PumpAddresses,
    SystemAddresses,
)
from src.core.wallet import Wallet
from src.trading.base import TokenInfo, Trader, TradeResult
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Discriminator for the buy instruction
EXPECTED_DISCRIMINATOR: Final[bytes] = struct.pack("<Q", 16927863322537952870)


class TokenBuyer(Trader):
    """Handles buying tokens on pump.fun."""

    def __init__(
        self,
        client: SolanaClient,
        wallet: Wallet,
        curve_manager: BondingCurveManager,
        priority_fee_manager: PriorityFeeManager,
        amount: float,
        slippage: float = 0.01,
        max_retries: int = 5,

        min_price: float = None,
        max_price: float = None,
        dry_run: bool = False,
        max_price_drop_threshold: float = 0.15,  # Максимальное падение цены (15%)
        price_stability_check: bool = False,     # Проверка стабильности цены
        min_stability_period: int = 15,          # Период стабильности (сек)
        min_growth_checks: int = 3,              # Минимум проверок роста
        min_growth_percentage: float = 0.01,     # Минимальный процент роста (1%)
        allow_zero_growth: int = 0,              # Разрешить N проверок с 0% ростом
        gap_tolerance: bool = False,             # Учитывать пропуски pump.fun API
    ):
        """Initialize token buyer.

        Args:
            client: Solana client for RPC calls
            wallet: Wallet for signing transactions
            curve_manager: Bonding curve manager
            amount: Amount of SOL to spend
            slippage: Slippage tolerance (0.01 = 1%)
            max_retries: Maximum number of retry attempts

        """
        self.client = client
        self.wallet = wallet
        self.curve_manager = curve_manager
        self.priority_fee_manager = priority_fee_manager
        self.amount = amount
        self.slippage = slippage
        self.max_retries = max_retries

        self.min_price = min_price
        self.max_price = max_price
        self.dry_run = dry_run
        self.max_price_drop_threshold = max_price_drop_threshold
        self.price_stability_check = price_stability_check
        self.min_stability_period = min_stability_period
        self.min_growth_checks = min_growth_checks
        self.min_growth_percentage = min_growth_percentage
        self.allow_zero_growth = allow_zero_growth
        self.gap_tolerance = gap_tolerance



    async def execute(self, token_info: TokenInfo, *args, **kwargs) -> TradeResult:
        """Execute buy operation.

        Args:
            token_info: Token information

        Returns:
            TradeResult with buy outcome
        """
        try:
            # Convert amount to lamports
            amount_lamports = int(self.amount * LAMPORTS_PER_SOL)

            # 🔍 ПЕРВИЧНАЯ ПРОВЕРКА ЦЕНЫ
            curve_state = await self.curve_manager.get_curve_state(token_info.bonding_curve)
            initial_price = curve_state.calculate_price()
            token_amount = self.amount / initial_price

            logger.info(f"🔍 INITIAL PRICE CHECK: {token_info.symbol} price={initial_price:.2e} SOL")
            # Примечание: RPC логирование происходит в MultiRPC автоматически

            # 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА ЦЕНЫ ПЕРЕД ПОКУПКОЙ
            if self.min_price is not None and initial_price < self.min_price:
                error_msg = f"PRICE PROTECTION: Initial price {initial_price:.2e} SOL < min_price {self.min_price:.2e} SOL. Purchase BLOCKED!"
                logger.error(error_msg)
                return TradeResult(success=False, error_message=error_msg, used_rpc_client=self.client)

            if self.max_price is not None and initial_price > self.max_price:
                error_msg = f"PRICE PROTECTION: Initial price {initial_price:.2e} SOL > max_price {self.max_price:.2e} SOL. Purchase BLOCKED!"
                logger.error(error_msg)
                return TradeResult(success=False, error_message=error_msg, used_rpc_client=self.client)

            # 🚨 LAST-SECOND PRICE VALIDATION - КРИТИЧЕСКАЯ ПРОВЕРКА!
            logger.warning(f"🔄 LAST-SECOND PRICE CHECK: Проверяем цену {token_info.symbol} перед отправкой транзакции...")

            # 🔧 ИСПРАВЛЕНО: Получаем свежую цену через ТОТ ЖЕ RPC что и execution
            from src.core.curve import BondingCurveManager
            dedicated_curve_manager = BondingCurveManager(self.client)
            fresh_curve_state = await dedicated_curve_manager.get_curve_state(token_info.bonding_curve)
            current_price = fresh_curve_state.calculate_price()

            # Вычисляем изменение цены
            price_change_percent = ((current_price - initial_price) / initial_price) * 100

            logger.warning(f"🔍 PRICE VALIDATION: {token_info.symbol}")
            logger.warning(f"   Initial price: {initial_price:.2e} SOL")
            logger.warning(f"   Current price: {current_price:.2e} SOL")
            logger.warning(f"   Change: {price_change_percent:+.2f}%")

            # 🚨 ЗАЩИТА ОТ ОБВАЛА: используем настройку из конфигурации
            max_price_drop = -(self.max_price_drop_threshold * 100)  # Конвертируем в проценты
            if price_change_percent < max_price_drop:
                error_msg = f"🚨 PRICE CRASH DETECTED! {token_info.symbol} price dropped {price_change_percent:.2f}% (>{max_price_drop}%). Purchase CANCELLED to protect capital!"
                logger.error(error_msg)
                return TradeResult(success=False, error_message=error_msg, used_rpc_client=self.client)

            # 🚨 ЗАЩИТА ОТ ПАМП-ДАМПА: если цена выросла больше чем на 50% - тоже отменяем
            max_price_pump = 50.0  # Максимальный рост цены в %
            if price_change_percent > max_price_pump:
                error_msg = f"🚨 PUMP DETECTED! {token_info.symbol} price pumped {price_change_percent:.2f}% (>{max_price_pump}%). Purchase CANCELLED - likely pump&dump!"
                logger.error(error_msg)
                return TradeResult(success=False, error_message=error_msg, used_rpc_client=self.client)

            # Используем актуальную цену для расчетов
            token_price_sol = current_price
            token_amount = self.amount / token_price_sol

            logger.warning(f"✅ PRICE VALIDATION PASSED: {token_info.symbol} safe to buy at {token_price_sol:.2e} SOL")

            # Calculate maximum SOL to spend with slippage
            max_amount_lamports = int(amount_lamports * (1 + self.slippage))

            # 🧪 DRY RUN MODE - СИМУЛЯЦИЯ БЕЗ РЕАЛЬНОЙ ПОКУПКИ
            if self.dry_run:
                logger.info("🧪 DRY RUN: Simulating buy transaction (no real purchase)")

                # Симулируем успешную транзакцию
                import time
                mint_str = str(token_info.mint)
                fake_signature = f"DRY_RUN_{int(time.time())}_BUY_{mint_str[:8]}"

                return TradeResult(
                    success=True,
                    tx_signature=fake_signature,
                    amount=token_amount,
                    price=token_price_sol,
                    error_message=None,
                    used_rpc_client=self.client
                )

            associated_token_account = self.wallet.get_associated_token_address(
                token_info.mint
            )

            tx_signature = await self._send_buy_transaction(
                token_info,
                associated_token_account,
                token_amount,
                max_amount_lamports,
            )

            logger.info(
                f"Buying {token_amount:.6f} tokens at {token_price_sol:.8f} SOL per token"
            )
            logger.info(
                f"Total cost: {self.amount:.6f} SOL (max: {max_amount_lamports / LAMPORTS_PER_SOL:.6f} SOL)"
            )

            success = await self.client.confirm_transaction(tx_signature)

            if success:
                # 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем РЕАЛЬНЫЙ результат выполнения программы
                try:
                    actual_price, actual_tokens = await self._get_actual_execution_price(tx_signature, token_info)

                    # 🔍 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Убеждаемся что токены действительно получены
                    if actual_tokens <= 0 or actual_price <= 0:
                        error_msg = f"🚨 PROGRAM EXECUTION FAILED: Transaction confirmed but no tokens received! actual_tokens={actual_tokens}, actual_price={actual_price}"
                        logger.error(error_msg)
                        return TradeResult(
                            success=False,
                            error_message=error_msg,
                            tx_signature=tx_signature,
                            used_rpc_client=self.client
                        )

                    logger.info(f"Buy transaction confirmed: {tx_signature}")
                    logger.info(f"Actual price paid to bonding curve: {actual_price:.8f} SOL per token")

                    return TradeResult(
                        success=True,
                        tx_signature=tx_signature,
                        amount=actual_tokens,      # Actual tokens received
                        price=actual_price,        # Actual price based on bonding curve SOL flow
                        used_rpc_client=self.client
                    )

                except Exception as e:
                    # 🚨 ОШИБКА ВЫПОЛНЕНИЯ ПРОГРАММЫ: Транзакция confirmed но программа failed
                    error_msg = f"🚨 PROGRAM EXECUTION FAILED: Transaction confirmed but program failed: {e}"
                    logger.error(error_msg)
                    return TradeResult(
                        success=False,
                        error_message=error_msg,
                        tx_signature=tx_signature,
                        used_rpc_client=self.client
                    )
            else:
                return TradeResult(
                    success=False,
                    error_message=f"Transaction failed to confirm: {tx_signature}",
                    used_rpc_client=self.client
                )

        except Exception as e:
            logger.error(f"Buy operation failed: {e!s}")
            return TradeResult(success=False, error_message=str(e), used_rpc_client=self.client)

    async def _send_buy_transaction(
        self,
        token_info: TokenInfo,
        associated_token_account: Pubkey,
        token_amount: float,
        max_amount_lamports: int,
    ) -> str:
        """Send buy transaction.

        Args:
            token_info: Token information
            associated_token_account: User's token account
            token_amount: Amount of tokens to buy
            max_amount_lamports: Maximum SOL to spend in lamports

        Returns:
            Transaction signature

        Raises:
            Exception: If transaction fails after all retries
        """
        accounts = [
            AccountMeta(
                pubkey=PumpAddresses.GLOBAL, is_signer=False, is_writable=False
            ),
            AccountMeta(pubkey=PumpAddresses.FEE, is_signer=False, is_writable=True),
            AccountMeta(pubkey=token_info.mint, is_signer=False, is_writable=False),
            AccountMeta(
                pubkey=token_info.bonding_curve, is_signer=False, is_writable=True
            ),
            AccountMeta(
                pubkey=token_info.associated_bonding_curve,
                is_signer=False,
                is_writable=True,
            ),
            AccountMeta(
                pubkey=associated_token_account, is_signer=False, is_writable=True
            ),
            AccountMeta(pubkey=self.wallet.pubkey, is_signer=True, is_writable=True),
            AccountMeta(
                pubkey=SystemAddresses.PROGRAM, is_signer=False, is_writable=False
            ),
            AccountMeta(
                pubkey=SystemAddresses.TOKEN_PROGRAM, is_signer=False, is_writable=False
            ),
            AccountMeta(
                pubkey=token_info.creator_vault, is_signer=False, is_writable=True
            ),
            AccountMeta(
                pubkey=PumpAddresses.EVENT_AUTHORITY, is_signer=False, is_writable=False
            ),
            AccountMeta(
                pubkey=PumpAddresses.PROGRAM, is_signer=False, is_writable=False
            ),
        ]

        # Prepare idempotent create ATA instruction: it will not fail if ATA already exists
        idempotent_ata_ix = create_idempotent_associated_token_account(
            self.wallet.pubkey,
            self.wallet.pubkey,
            token_info.mint,
            SystemAddresses.TOKEN_PROGRAM
        )

        # Prepare buy instruction data
        token_amount_raw = int(token_amount * 10**TOKEN_DECIMALS)
        data = (
            EXPECTED_DISCRIMINATOR
            + struct.pack("<Q", token_amount_raw)
            + struct.pack("<Q", max_amount_lamports)
        )
        buy_ix = Instruction(PumpAddresses.PROGRAM, data, accounts)

        try:
            return await self.client.build_and_send_transaction(
                [idempotent_ata_ix, buy_ix],
                self.wallet.keypair,
                skip_preflight=True,
                max_retries=self.max_retries,
                priority_fee=await self.priority_fee_manager.calculate_priority_fee(
                    self._get_relevant_accounts(token_info)
                ),
            )
        except Exception as e:
            logger.error(f"Buy transaction failed: {e!s}")
            raise


    async def _get_actual_execution_price(self, tx_signature: str, token_info: TokenInfo) -> tuple[float, float]:
        """Get actual execution price from bonding curve SOL balance changes."""
        try:
            client = await self.client.get_client()

            tx_response = await client.get_transaction(
                tx_signature, 
                encoding="jsonParsed",
                commitment="confirmed",
                max_supported_transaction_version=0
            )
            
            if not tx_response.value or not tx_response.value.transaction:
                raise ValueError("Transaction not found")
                
            meta = tx_response.value.transaction.meta
            if not meta or not meta.pre_balances or not meta.post_balances:
                raise ValueError("Transaction balance data not found")
                
            # Get accounts - they're ParsedAccountTxStatus objects, need to extract pubkey
            accounts = tx_response.value.transaction.transaction.message.account_keys
            
            # Find bonding curve account index in the transaction
            bonding_curve_index = None
            for i, account in enumerate(accounts):
                # Extract pubkey from ParsedAccountTxStatus object
                account_pubkey = str(account.pubkey) if hasattr(account, 'pubkey') else str(account)
                
                if account_pubkey == str(token_info.bonding_curve):
                    bonding_curve_index = i
                    break
                    
            if bonding_curve_index is None:
                raise ValueError("Bonding curve not found in transaction accounts")
                
            pre_balance_lamports = meta.pre_balances[bonding_curve_index]
            post_balance_lamports = meta.post_balances[bonding_curve_index]

            sol_sent_to_curve = (post_balance_lamports - pre_balance_lamports) / LAMPORTS_PER_SOL
            
            if sol_sent_to_curve <= 0:
                raise ValueError(f"No SOL sent to bonding curve: {sol_sent_to_curve}")
            
            tokens_received = await self._get_tokens_received_from_tx(tx_response, token_info)
            
            if tokens_received == 0:
                raise ValueError("Cannot compute execution price: zero tokens received")
            actual_price = sol_sent_to_curve / tokens_received
            
            logger.info(f"Bonding curve received: {sol_sent_to_curve:.6f} SOL")
            logger.info(f"We received: {tokens_received:.6f} tokens")
            logger.info(f"Actual execution price: {actual_price:.8f} SOL per token")
            
            return actual_price, tokens_received
            
        except Exception as e:
            logger.warning(f"Failed to get actual execution price from bonding curve: {e}")

            # 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Если ошибка связана с отсутствием SOL в bonding curve - это FAILED транзакция
            error_str = str(e).lower()
            if "no sol sent to bonding curve" in error_str or "bonding curve" in error_str:
                logger.error(f"🚨 PROGRAM EXECUTION FAILED: {e}")
                # Возвращаем 0.0, 0.0 чтобы основная логика поняла что транзакция failed
                return 0.0, 0.0

            # Fallback estimate только для других типов ошибок (например, RPC недоступен)
            try:
                tokens_received = self.amount / await self.curve_manager.calculate_price(token_info.bonding_curve)
                if tokens_received == 0:
                   logger.error("Fallback failed – unable to determine tokens received")
                   return 0.0, 0.0
                logger.warning(f"🔄 Using fallback estimate: {tokens_received:.6f} tokens")
                return self.amount / tokens_received, tokens_received
            except Exception as fallback_error:
                logger.error(f"Fallback calculation failed: {fallback_error}")
                return 0.0, 0.0


    async def _get_tokens_received_from_tx(self, tx_response, token_info: TokenInfo) -> float:
        """Extract tokens received from transaction token balance changes."""
        meta = tx_response.value.transaction.meta
        
        pre_token_balance = 0
        post_token_balance = 0
        
        wallet_str = str(self.wallet.pubkey)
        mint_str = str(token_info.mint)
        
        if meta.pre_token_balances:
            for balance in meta.pre_token_balances:
                # Convert to string for comparison
                balance_owner = str(balance.owner) if hasattr(balance, 'owner') else str(getattr(balance, 'owner', ''))
                balance_mint = str(balance.mint) if hasattr(balance, 'mint') else str(getattr(balance, 'mint', ''))
                
                if balance_owner == wallet_str and balance_mint == mint_str:
                    try:
                        # Try multiple ways to get the amount
                        if hasattr(balance, 'ui_token_amount'):
                            amount_obj = balance.ui_token_amount
                            if hasattr(amount_obj, 'amount') and amount_obj.amount is not None:
                                pre_token_balance = int(amount_obj.amount)
                            elif hasattr(amount_obj, 'ui_amount') and amount_obj.ui_amount is not None:
                                pre_token_balance = int(float(amount_obj.ui_amount) * (10**TOKEN_DECIMALS))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error parsing pre-token balance: {e}")
                    break
                    
        # Check post-token balances  
        if meta.post_token_balances:
            for balance in meta.post_token_balances:
                # Convert to string for comparison
                balance_owner = str(balance.owner) if hasattr(balance, 'owner') else str(getattr(balance, 'owner', ''))
                balance_mint = str(balance.mint) if hasattr(balance, 'mint') else str(getattr(balance, 'mint', ''))
                
                if balance_owner == wallet_str and balance_mint == mint_str:
                    try:
                        # Try multiple ways to get the amount
                        if hasattr(balance, 'ui_token_amount'):
                            amount_obj = balance.ui_token_amount
                            if hasattr(amount_obj, 'amount') and amount_obj.amount is not None:
                                post_token_balance = int(amount_obj.amount)
                            elif hasattr(amount_obj, 'ui_amount') and amount_obj.ui_amount is not None:
                                post_token_balance = int(float(amount_obj.ui_amount) * (10**TOKEN_DECIMALS))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error parsing post-token balance: {e}")
                    break
        
        # Calculate tokens received
        if pre_token_balance == 0 and post_token_balance > 0:
            tokens_received_raw = post_token_balance
        else:
            tokens_received_raw = post_token_balance - pre_token_balance
        
        if tokens_received_raw <= 0:
            logger.warning("Token balance search failed.")
            logger.error("Cannot determine tokens received from transaction")
            return 0.0

        return tokens_received_raw / 10**TOKEN_DECIMALS