from abc import ABC, abstractmethod
from src.trading.position import Position, ExitReason

class ExitStrategy(ABC):
    @abstractmethod
    def should_exit(self, position: Position, current_price: float) -> tuple[bool, ExitReason | None]:
        pass

class TimeBasedExit(ExitStrategy):
    def should_exit(self, position: Position, current_price: float):
        from datetime import datetime, timezone
        if position.max_hold_time is not None:
            elapsed = (datetime.now(timezone.utc) - position.entry_time).total_seconds()
            if elapsed >= position.max_hold_time:
                return True, ExitReason.TIME_BASED
        return False, None

class TpSlExit(ExitStrategy):
    def should_exit(self, position: Position, current_price: float):
        return position.should_exit(current_price)

class TrailingStopExit(ExitStrategy):
    def should_exit(self, position: Position, current_price: float):
        return position.should_exit(current_price)

# Factory

def get_exit_strategy(strategy_name: str) -> ExitStrategy:
    if strategy_name == "time_based":
        return TimeBasedExit()
    if strategy_name == "tp_sl":
        return TpSlExit()
    if strategy_name == "trailing_stop":
        return TrailingStopExit()
    raise ValueError(f"Unknown exit strategy: {strategy_name}")
