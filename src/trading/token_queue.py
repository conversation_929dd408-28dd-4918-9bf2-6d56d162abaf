
import asyncio
from typing import List, Optional, Any, Tuple
from src.trading.base import TokenInfo



import time

class TokenQueue:
    def __init__(self, maxsize: int = 100, max_active: int = 1, recheck_delay: int = 18, max_recheck_attempts: int = 3, second_recheck_delay: int = 18, second_max_recheck_attempts: int = 3):
        self._queue = asyncio.PriorityQueue(maxsize=maxsize)
        self.max_active = max_active
        self._active = 0
        self._lock = asyncio.Lock()
        # Отстойник: {token.mint: (token, next_check_time, attempts, detected_time, quarantine_type)}
        self._recheck_delay = recheck_delay
        self._max_recheck_attempts = max_recheck_attempts
        self._second_recheck_delay = second_recheck_delay
        self._second_max_recheck_attempts = second_max_recheck_attempts
        self._quarantine = {}

    async def put(self, token: TokenInfo, priority: int = 100):
        # Lower priority value = higher priority
        timestamp = time.monotonic()
        if not self._queue.full():
            await self._queue.put((priority, timestamp, token))
            return True
        # If full: try to evict lowest-priority if new token is higher priority
        items = []
        try:
            while not self._queue.empty():
                items.append(self._queue.get_nowait())
        except asyncio.QueueEmpty:
            pass
        if not items:
            return False
        # Find max-priority (lowest real priority)
        max_priority = max(items, key=lambda x: x[0])[0]
        if priority < max_priority:
            # Remove one lowest-priority, insert new
            removed = False
            new_items = []
            for entry in items:
                p = entry[0]
                if not removed and p == max_priority:
                    removed = True
                    continue
                new_items.append(entry)
            for entry in new_items:
                await self._queue.put(entry)
            await self._queue.put((priority, timestamp, token))
            return True
        # Otherwise, restore all and reject
        for entry in items:
            await self._queue.put(entry)
        return False


    async def get(self) -> TokenInfo:
        priority, timestamp, token = await self._queue.get()
        return token

    def put_in_quarantine(self, token: TokenInfo, attempts: int = 1, detected_time: float = None, quarantine_type: str = None):
        """Поместить токен в отстойник для повторной проверки, сохраняя время первого обнаружения.
        quarantine_type: None (обычный), 'price', 'holders' и т.д.
        """
        if detected_time is None:
            detected_time = time.time()
        # Выбор задержки и max_attempts по типу quarantine
        if quarantine_type in ("price", "holders"):
            delay = self._second_recheck_delay
        else:
            delay = self._recheck_delay
        print(f"[QUARANTINE DEBUG] put_in_quarantine: {token.mint}, attempts={attempts}, next_check={time.time() + delay}, detected_time={detected_time}, quarantine_type={quarantine_type}")
        self._quarantine[token.mint] = (token, time.time() + delay, attempts, detected_time, quarantine_type)

    def get_quarantine_ready(self) -> list:
        """Вернуть токены, готовые к повторной проверке, вместе с detected_time и типом quarantine."""
        now = time.time()
        ready = []
        for mint, value in list(self._quarantine.items()):
            # value: (token, next_check, attempts, detected_time, quarantine_type)
            if len(value) == 5:
                token, next_check, attempts, detected_time, quarantine_type = value
            elif len(value) == 4:
                token, next_check, attempts, detected_time = value
                quarantine_type = None
            else:
                token, next_check, attempts = value
                detected_time = None
                quarantine_type = None
            print(f"[QUARANTINE DEBUG] get_quarantine_ready: {mint}, now={now}, next_check={next_check}, attempts={attempts}, detected_time={detected_time}, quarantine_type={quarantine_type}")
            if now >= next_check:
                print(f"[QUARANTINE DEBUG] token {mint} is ready for recheck after {attempts} attempts!")
                ready.append((token, attempts, detected_time, quarantine_type))
                del self._quarantine[mint]
        return ready

    def can_recheck(self, attempts: int, quarantine_type: str = None) -> bool:
        if quarantine_type in ("price", "holders"):
            return attempts < self._second_max_recheck_attempts
        return attempts < self._max_recheck_attempts

    async def task_done(self):
        self._queue.task_done()

    async def can_start_new(self) -> bool:
        async with self._lock:
            return self._active < self.max_active

    async def start_trade(self):
        async with self._lock:
            self._active += 1

    async def finish_trade(self):
        async with self._lock:
            self._active = max(0, self._active - 1)

    def qsize(self) -> int:
        return self._queue.qsize()
