"""
Position management for take profit/stop loss functionality.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import time

from solders.pubkey import Pubkey


class ExitReason(Enum):
    """Reasons for position exit."""
    TAKE_PROFIT = "take_profit"
    STOP_LOSS = "stop_loss"
    PRE_TRAILING_STOP = "pre_trailing_stop"  # New: stop loss before trailing activation
    TRAILING_STOP = "trailing_stop"
    MAX_HOLD_TIME = "max_hold_time"
    INACTIVITY_TIMEOUT = "inactivity_timeout"  # New: exit due to token inactivity
    MANUAL = "manual"


@dataclass
class Position:
    """Represents an active trading position."""
    
    # Token information
    mint: Pubkey
    symbol: str
    
    # Position details
    entry_price: float
    quantity: float
    entry_time: datetime
    
    # Exit conditions
    take_profit_price: float | None = None
    stop_loss_price: float | None = None
    pre_trailing_stop_loss: float | None = None  # e.g. 0.12 for 12% profit stop before trailing activation
    max_hold_time: int | None = None  # seconds
    trailing_stop_percentage: float | None = None  # e.g. 0.1 for 10%
    trailing_stop_activation: float | None = None  # e.g. 0.15 for 15% profit before activation
    _trailing_activated: bool = False
    _max_price: float | None = None
    _max_profit_reached: float = 0.0  # Track maximum profit percentage reached
    
    # Status
    is_active: bool = True
    exit_reason: ExitReason | None = None
    exit_price: float | None = None
    exit_time: datetime | None = None

    # Dedicated RPC support
    dedicated_rpc_id: str | None = None  # ID зарезервированного RPC для этой позиции
    position_id: str = field(default_factory=lambda: f"pos_{int(time.time() * 1000)}")  # Уникальный ID позиции
    
    @classmethod
    def create_from_buy_result(
        cls,
        mint: Pubkey,
        symbol: str,
        entry_price: float,
        quantity: float,
        take_profit_percentage: float | None = None,
        stop_loss_percentage: float | None = None,
        pre_trailing_stop_loss: float | None = None,
        max_hold_time: int | None = None,
        trailing_stop_percentage: float | None = None,
        trailing_stop_activation: float | None = None,
    ) -> "Position":
        """Create a position from a successful buy transaction.
        Args:
            mint: Token mint address
            symbol: Token symbol
            entry_price: Price at which position was entered
            quantity: Quantity of tokens purchased
            take_profit_percentage: Take profit percentage (0.5 = 50% profit)
            stop_loss_percentage: Stop loss percentage (0.2 = 20% loss)
            max_hold_time: Maximum hold time in seconds
            trailing_stop_percentage: Trailing stop loss percent (0.1 = 10%)
            trailing_stop_activation: Profit percent to activate trailing stop (0.15 = 15%)
        Returns:
            Position instance
        """
        take_profit_price = None
        if take_profit_percentage is not None:
            take_profit_price = entry_price * (1 + take_profit_percentage)
        stop_loss_price = None
        if stop_loss_percentage is not None:
            stop_loss_price = entry_price * (1 - stop_loss_percentage)
        return cls(
            mint=mint,
            symbol=symbol,
            entry_price=entry_price,
            quantity=quantity,
            entry_time=datetime.utcnow(),
            take_profit_price=take_profit_price,
            stop_loss_price=stop_loss_price,
            pre_trailing_stop_loss=pre_trailing_stop_loss,
            max_hold_time=max_hold_time,
            trailing_stop_percentage=trailing_stop_percentage,
            trailing_stop_activation=trailing_stop_activation,
            _trailing_activated=False,
            _max_price=None,
            _max_profit_reached=0.0,
        )
    
    def should_exit(self, current_price: float) -> tuple[bool, ExitReason | None]:
        """Check if position should be exited based on current conditions, including trailing stop.
        Args:
            current_price: Current token price
        Returns:
            Tuple of (should_exit, exit_reason)
        """
        if not self.is_active:
            return False, None

        # Update max price and max profit for tracking
        current_profit_percentage = (current_price - self.entry_price) / self.entry_price
        if current_profit_percentage > self._max_profit_reached:
            self._max_profit_reached = current_profit_percentage

        # Update max price for trailing stop
        if self.trailing_stop_percentage is not None:
            if self._max_price is None:
                self._max_price = self.entry_price
            if current_price > self._max_price:
                self._max_price = current_price

            # Activate trailing stop if activation threshold is reached
            if (
                not self._trailing_activated
                and self.trailing_stop_activation is not None
                and (self._max_price - self.entry_price) / self.entry_price >= self.trailing_stop_activation
            ):
                self._trailing_activated = True

        # Check take profit ONLY if trailing stop is not activated
        if self.take_profit_price and current_price >= self.take_profit_price:
            # If trailing stop is configured and activated, let it handle the exit
            if self.trailing_stop_percentage is not None and self._trailing_activated:
                # Trailing stop is active, check trailing condition instead of take profit
                # Calculate trailing stop based on profit percentage, not price percentage
                max_profit_pct = (self._max_price - self.entry_price) / self.entry_price
                trailing_profit_pct = max_profit_pct - self.trailing_stop_percentage
                trailing_stop_price = self.entry_price * (1 + trailing_profit_pct)
                if current_price <= trailing_stop_price:
                    return True, ExitReason.TRAILING_STOP
                # Continue monitoring with trailing stop, don't exit on take profit
            else:
                # No trailing stop or not activated yet, use regular take profit
                return True, ExitReason.TAKE_PROFIT

        # Check trailing stop condition if activated (for price drops)
        if (self.trailing_stop_percentage is not None
            and self._trailing_activated):
            # Calculate trailing stop based on profit percentage, not price percentage
            max_profit_pct = (self._max_price - self.entry_price) / self.entry_price
            trailing_profit_pct = max_profit_pct - self.trailing_stop_percentage
            trailing_stop_price = self.entry_price * (1 + trailing_profit_pct)
            if current_price <= trailing_stop_price:
                return True, ExitReason.TRAILING_STOP

        # Check stop loss FIRST (emergency protection)
        if self.stop_loss_price and current_price <= self.stop_loss_price:
            return True, ExitReason.STOP_LOSS

        # 🛡️ NEW: Check pre-trailing stop loss (profit protection)
        if (self.pre_trailing_stop_loss is not None
            and not self._trailing_activated):  # Only if trailing is NOT activated yet (or disabled)

            # Check if we have reached the pre-trailing profit level in the past and are now falling back
            if self._max_profit_reached >= self.pre_trailing_stop_loss:
                # We reached the profit level, now check if we're falling back to the stop level
                pre_trailing_stop_price = self.entry_price * (1 + self.pre_trailing_stop_loss)
                if current_price <= pre_trailing_stop_price:
                    return True, ExitReason.PRE_TRAILING_STOP

        # Check max hold time
        if self.max_hold_time:
            elapsed_time = (datetime.utcnow() - self.entry_time).total_seconds()
            if elapsed_time >= self.max_hold_time:
                return True, ExitReason.MAX_HOLD_TIME

        return False, None
    
    def close_position(self, exit_price: float, exit_reason: ExitReason) -> None:
        """Close the position with exit details.
        
        Args:
            exit_price: Price at which position was exited
            exit_reason: Reason for exit
        """
        self.is_active = False
        self.exit_price = exit_price
        self.exit_reason = exit_reason
        self.exit_time = datetime.utcnow()
    
    def get_pnl(self, current_price: float | None = None) -> dict:
        """Calculate profit/loss for the position.
        
        Args:
            current_price: Current price (uses exit_price if position is closed)
            
        Returns:
            Dictionary with PnL information
        """
        if self.is_active and current_price is None:
            raise ValueError("current_price required for active position")
            
        price_to_use = self.exit_price if not self.is_active else current_price
        if price_to_use is None:
            raise ValueError("No price available for PnL calculation")
            
        price_change = price_to_use - self.entry_price
        price_change_pct = (price_change / self.entry_price) * 100
        unrealized_pnl = price_change * self.quantity
        
        return {
            "entry_price": self.entry_price,
            "current_price": price_to_use,
            "price_change": price_change,
            "price_change_pct": price_change_pct,
            "unrealized_pnl_sol": unrealized_pnl,
            "quantity": self.quantity,
        }

    def set_dedicated_rpc(self, rpc_id: str) -> None:
        """Устанавливает ID зарезервированного RPC для этой позиции."""
        self.dedicated_rpc_id = rpc_id

    def has_dedicated_rpc(self) -> bool:
        """Проверяет, есть ли у позиции зарезервированный RPC."""
        return self.dedicated_rpc_id is not None

    def get_position_id(self) -> str:
        """Возвращает уникальный ID позиции."""
        return self.position_id

    def __str__(self) -> str:
        """String representation of position."""
        if self.is_active:
            status = "ACTIVE"
        elif self.exit_reason:
            status = f"CLOSED ({self.exit_reason.value})"
        else:
            status = "CLOSED (UNKNOWN)"
        rpc_info = f" [RPC: {self.dedicated_rpc_id}]" if self.dedicated_rpc_id else ""
        return f"Position({self.symbol}: {self.quantity:.6f} @ {self.entry_price:.8f} SOL - {status}{rpc_info})"


@dataclass
class ActivityBasedPosition(Position):
    """Position with activity-based hold time management."""

    # Activity tracking
    last_price: float = field(default=0.0)
    last_activity_time: float = field(default_factory=time.time)
    price_history: list = field(default_factory=list)

    # Activity-based config
    activity_config: dict = field(default_factory=dict)

    def __post_init__(self):
        """Initialize activity tracking."""
        self.last_price = self.entry_price
        self.last_activity_time = time.time()

        # Default activity config
        default_config = {
            "max_absolute": 3600,
            "inactivity_timeout": 30,
            "price_change_threshold": 5,
            "check_interval": 15,
            "smart_intervals": {
                "high_activity": 10,
                "low_activity": 15,
                "stagnant": 15
            }
        }

        if self.activity_config:
            default_config.update(self.activity_config)
        self.activity_config = default_config

    def update_price(self, current_price: float) -> None:
        """Update price and check for activity."""
        current_time = time.time()

        # Calculate price change percentage
        price_change = 0
        if self.last_price > 0:
            price_change = abs((current_price - self.last_price) / self.last_price) * 100

            # Check if this is significant activity
            if price_change >= self.activity_config["price_change_threshold"]:
                self.last_activity_time = current_time

        # Update price history (keep last 10 prices)
        self.price_history.append({
            "price": current_price,
            "time": current_time,
            "change": price_change
        })

        if len(self.price_history) > 10:
            self.price_history.pop(0)

        self.last_price = current_price

    def get_activity_level(self) -> str:
        """Determine current activity level."""
        if not self.price_history:
            return "low_activity"

        # Check recent price changes
        recent_changes = [p["change"] for p in self.price_history[-3:]]
        avg_change = sum(recent_changes) / len(recent_changes) if recent_changes else 0

        threshold = self.activity_config["price_change_threshold"]

        if avg_change >= threshold:
            return "high_activity"
        elif avg_change >= threshold / 2:
            return "low_activity"
        else:
            return "stagnant"

    def get_next_check_interval(self) -> int:
        """Get the next check interval based on activity."""
        activity_level = self.get_activity_level()
        return self.activity_config["smart_intervals"][activity_level]

    def should_exit_activity(self, current_price: float) -> tuple[bool, ExitReason | None]:
        """Check if position should exit based on activity."""
        current_time = time.time()

        # Update price first
        self.update_price(current_price)

        # Check absolute maximum time
        elapsed_total = (datetime.utcnow() - self.entry_time).total_seconds()
        if elapsed_total >= self.activity_config["max_absolute"]:
            return True, ExitReason.MAX_HOLD_TIME

        # Check inactivity timeout
        time_since_activity = current_time - self.last_activity_time
        if time_since_activity >= self.activity_config["inactivity_timeout"]:
            return True, ExitReason.INACTIVITY_TIMEOUT

        # Check regular TP/SL conditions
        return self.should_exit(current_price)

    @classmethod
    def create_from_buy_result(
        cls,
        mint: Pubkey,
        symbol: str,
        entry_price: float,
        quantity: float,
        take_profit_percentage: float | None = None,
        stop_loss_percentage: float | None = None,
        pre_trailing_stop_loss: float | None = None,
        activity_config: dict | None = None,
        trailing_stop_percentage: float | None = None,
        trailing_stop_activation: float | None = None,
    ) -> "ActivityBasedPosition":
        """Create an activity-based position from buy result."""

        # Calculate TP/SL prices
        take_profit_price = None
        stop_loss_price = None

        if take_profit_percentage:
            take_profit_price = entry_price * (1 + take_profit_percentage)

        if stop_loss_percentage:
            stop_loss_price = entry_price * (1 - stop_loss_percentage)

        return cls(
            mint=mint,
            symbol=symbol,
            entry_price=entry_price,
            quantity=quantity,
            entry_time=datetime.utcnow(),
            take_profit_price=take_profit_price,
            stop_loss_price=stop_loss_price,
            pre_trailing_stop_loss=pre_trailing_stop_loss,
            trailing_stop_percentage=trailing_stop_percentage,
            trailing_stop_activation=trailing_stop_activation,
            _trailing_activated=False,
            _max_price=None,
            _max_profit_reached=0.0,
            activity_config=activity_config or {}
        )