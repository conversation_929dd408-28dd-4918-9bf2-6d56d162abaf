import threading
from typing import List, Dict, Optional
import time
import asyncio
from solana.rpc.async_api import AsyncClient

class MultiRpcProvider:
    """
    Simple round-robin multi-RPC provider for Solana.
    Usage:
        provider = MultiRpcProvider([
            {"url": "https://rpc1", "wss": "wss://rpc1/ws"},
            {"url": "https://rpc2", "wss": "wss://rpc2/ws"},
        ])
        rpc_url = provider.next_rpc()
        wss_url = provider.next_wss()
    """
    def __init__(self, endpoints: List[Dict[str, str]], fail_timeout: int = 120, max_rps: int = 12, request_delay: float = 0.1):
        self.endpoints = endpoints
        self._lock = threading.Lock()
        self._idx = 0
        self._count = len(endpoints)
        self._fail_timeout = fail_timeout  # seconds
        self._fail_map = {}  # idx: until_timestamp
        self._max_rps = max_rps
        self._request_delay = request_delay
        # Для каждого endpoint: {"ts": int, "count": int, "errors": int, "last_error": int}
        self._rate_map = {idx: {"ts": 0, "count": 0, "errors": 0, "last_error": 0} for idx in range(self._count)}
        self._recovery_check_interval = 30  # Проверка восстановления каждые 30 сек
        self._max_errors_before_ban = 3  # Максимум ошибок перед блокировкой

        # Создаем постоянные подключения для каждого RPC
        self._clients = {}  # idx: AsyncClient
        self._initialize_clients()

        if self._count == 0:
            raise ValueError("MultiRpcProvider: endpoints list is empty")

    def _initialize_clients(self):
        """Создает постоянные AsyncClient для каждого RPC эндпоинта."""
        for idx, endpoint in enumerate(self.endpoints):
            self._clients[idx] = AsyncClient(endpoint["url"])
            rpc_name = endpoint["url"].split('//')[1].split('.')[0] if '//' in endpoint["url"] else endpoint["url"][:20]
            print(f"[MultiRPC] Initialized client #{idx+1}: {rpc_name}")

    def get_next_client(self) -> tuple[AsyncClient, int]:
        """
        Возвращает AsyncClient с ПРИНУДИТЕЛЬНЫМ round-robin распределением.
        Переключается на следующий RPC после КАЖДОГО запроса для максимального распределения.
        """
        with self._lock:
            now = int(time.time())

            # Ищем доступный RPC, начиная с текущего
            for attempt in range(self._count):
                current_fail_until = self._fail_map.get(self._idx, 0)
                current_rate = self._rate_map[self._idx]

                # Сбрасываем счетчик только при смене секунды
                if current_rate["ts"] != now:
                    current_rate["ts"] = now
                    current_rate["count"] = 0

                # Проверяем доступность текущего RPC
                if now >= current_fail_until and current_rate["count"] < self._max_rps:
                    client = self._clients[self._idx]
                    current_rate["count"] += 1

                    # Логируем использование RPC
                    url = self.endpoints[self._idx]["url"]
                    rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
                    print(f"[MultiRPC] {rpc_name} ({current_rate['count']}/{self._max_rps}) - RPC #{self._idx + 1}")

                    # ПРИНУДИТЕЛЬНОЕ ПЕРЕКЛЮЧЕНИЕ: переходим к следующему RPC после КАЖДОГО запроса
                    self._idx = (self._idx + 1) % self._count

                    return client, (self._idx - 1) % self._count  # Возвращаем индекс использованного RPC

                # Текущий RPC недоступен - переходим к следующему
                self._idx = (self._idx + 1) % self._count

            # Все RPC недоступны - возвращаем первый как fallback
            client = self._clients[0]
            url = self.endpoints[0]["url"]
            rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
            print(f"[MultiRPC] {rpc_name} - emergency fallback (all RPC busy)")
            return client, 0

    def next_rpc(self) -> str:
        """Обратная совместимость - возвращает URL доступного RPC."""
        client, idx = self.get_next_client()
        if client is None:
            # Если все заняты, возвращаем первый доступный
            return self.endpoints[0]["url"]
        return self.endpoints[idx]["url"]

    def next_wss(self) -> str:
        """Возвращает WSS URL первого доступного RPC."""
        with self._lock:
            now = int(time.time())

            # Ищем первый доступный endpoint для WSS
            for i in range(self._count):
                if now >= self._fail_map.get(i, 0):
                    wss = self.endpoints[i]["wss"]
                    print(f"[MultiRpcProvider] Using WSS: {wss}")
                    return wss

            # Если все недоступны, возвращаем первый (WSS обычно стабильнее)
            wss = self.endpoints[0]["wss"]
            print(f"[MultiRpcProvider] Fallback WSS: {wss}")
            return wss

    def mark_failed(self, url: str, error_type: str = "unknown"):
        """Mark endpoint as failed and track errors for smart recovery."""
        with self._lock:
            for idx, ep in enumerate(self.endpoints):
                if ep["url"] == url:
                    self._mark_failed_by_idx(idx, error_type)
                    break

    def mark_failed_by_idx(self, idx: int, error_type: str = "unknown"):
        """Mark endpoint as failed by index - faster for ready clients."""
        with self._lock:
            self._mark_failed_by_idx(idx, error_type)

    def _mark_failed_by_idx(self, idx: int, error_type: str = "unknown"):
        """Internal method to mark endpoint as failed by index."""
        now = int(time.time())
        rate = self._rate_map[idx]
        rate["errors"] += 1
        rate["last_error"] = now

        # Если превышено количество ошибок - блокируем на длительное время
        if rate["errors"] >= self._max_errors_before_ban:
            until = now + self._fail_timeout
            self._fail_map[idx] = until
            url = self.endpoints[idx]["url"]
            rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
            print(f"[MultiRPC] ❌ {rpc_name} BLOCKED ({rate['errors']} errors, {error_type}) until {until}")
            # Сбрасываем счетчик ошибок после блокировки
            rate["errors"] = 0
        else:
            # Временная блокировка на короткое время
            until = now + 10  # 10 секунд
            self._fail_map[idx] = until
            url = self.endpoints[idx]["url"]
            rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
            print(f"[MultiRPC] ⚠️ {rpc_name} temp block ({rate['errors']}/{self._max_errors_before_ban} errors, {error_type})")

    def check_recovery(self):
        """Проверяет и восстанавливает заблокированные RPC."""
        with self._lock:
            now = int(time.time())
            recovered = []

            for idx in list(self._fail_map.keys()):
                if now >= self._fail_map[idx]:
                    # RPC готов к восстановлению
                    del self._fail_map[idx]
                    rate = self._rate_map[idx]
                    rate["errors"] = 0  # Сбрасываем счетчик ошибок

                    url = self.endpoints[idx]["url"]
                    rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
                    recovered.append(rpc_name)

            if recovered:
                print(f"[MultiRPC] ✅ Recovered: {', '.join(recovered)}")

    def get_status(self) -> dict:
        """Возвращает статус всех RPC."""
        with self._lock:
            now = int(time.time())
            status = {}

            for idx, ep in enumerate(self.endpoints):
                url = ep["url"]
                rpc_name = url.split('//')[1].split('.')[0] if '//' in url else url[:20]
                rate = self._rate_map[idx]

                is_blocked = idx in self._fail_map and now < self._fail_map[idx]
                block_until = self._fail_map.get(idx, 0)

                status[rpc_name] = {
                    "blocked": is_blocked,
                    "block_until": block_until if is_blocked else 0,
                    "errors": rate["errors"],
                    "requests_this_sec": rate["count"] if rate["ts"] == now else 0
                }

            return status

    def get_current(self) -> Dict[str, str]:
        with self._lock:
            return self.endpoints[self._idx]

    def all(self) -> List[Dict[str, str]]:
        return self.endpoints
