"""
Dedicated RPC Manager - управляет выделенными RPC для каждой торговой позиции.
Решает проблему рассинхронизации данных между разными RPC endpoints.
"""

import logging
from typing import Dict, List, Optional, Tuple
from src.core.client import SolanaClient
from src.core.multi_rpc import MultiRpcProvider

logger = logging.getLogger(__name__)


class DedicatedRpcManager:
    """
    Управляет выделенными RPC endpoints для торговых позиций.
    
    Концепция:
    - Каждая активная позиция получает dedicated RPC
    - Все операции с токеном (баланс, продажа) идут через ЭТОТ же RPC
    - После закрытия позиции RPC возвращается в общий пул
    - Обеспечивает консистентность данных для каждой позиции
    """
    
    def __init__(self, rpc_endpoints: List[Dict], min_dedicated_rpcs: int = 2, reserve_rpcs_for_discovery: int = 2):
        """
        Инициализация менеджера.
        
        Args:
            rpc_endpoints: Список RPC endpoints в формате [{"url": "..."}, ...]
            min_dedicated_rpcs: Минимум RPC для выделения позициям
            reserve_rpcs_for_discovery: Количество RPC зарезервированных для поиска токенов
        """
        self.all_endpoints = rpc_endpoints
        self.min_dedicated_rpcs = min_dedicated_rpcs
        self.reserve_rpcs_for_discovery = reserve_rpcs_for_discovery
        
        # Проверяем достаточность RPC
        total_needed = min_dedicated_rpcs + reserve_rpcs_for_discovery
        if len(rpc_endpoints) < total_needed:
            logger.warning(f"Недостаточно RPC endpoints! Нужно минимум {total_needed}, доступно {len(rpc_endpoints)}")
            logger.warning("Некоторые позиции могут использовать общие RPC")
        
        # Состояние менеджера
        self.available_rpcs: List[Tuple[int, Dict]] = []  # (index, endpoint)
        self.reserved_rpcs: Dict[str, Tuple[int, SolanaClient]] = {}  # position_id -> (rpc_index, client)
        self.discovery_rpcs: List[Tuple[int, Dict]] = []  # RPC для поиска токенов
        
        # Инициализируем пулы RPC
        self._initialize_rpc_pools()
        
        logger.info(f"🎯 DedicatedRpcManager инициализирован:")
        logger.info(f"   Всего RPC: {len(self.all_endpoints)}")
        logger.info(f"   Доступно для позиций: {len(self.available_rpcs)}")
        logger.info(f"   Зарезервировано для поиска: {len(self.discovery_rpcs)}")
    
    def _initialize_rpc_pools(self):
        """Инициализирует пулы RPC для разных задач."""
        # Резервируем последние RPC для поиска токенов
        discovery_count = min(self.reserve_rpcs_for_discovery, len(self.all_endpoints))
        
        for i, endpoint in enumerate(self.all_endpoints):
            if i >= len(self.all_endpoints) - discovery_count:
                # Последние RPC для поиска токенов
                self.discovery_rpcs.append((i, endpoint))
                logger.info(f"🔍 RPC #{i+1} зарезервирован для поиска токенов: {self._get_rpc_name(endpoint)}")
            else:
                # Остальные RPC доступны для позиций
                self.available_rpcs.append((i, endpoint))
                logger.info(f"💰 RPC #{i+1} доступен для позиций: {self._get_rpc_name(endpoint)}")
    
    def _get_rpc_name(self, endpoint: Dict) -> str:
        """Получает короткое имя RPC для логов."""
        url = endpoint["url"]
        if '//' in url:
            return url.split('//')[1].split('.')[0]
        return url[:20]
    
    def reserve_rpc_for_position(self, position_id: str) -> Optional[SolanaClient]:
        """
        Резервирует RPC для торговой позиции.
        
        Args:
            position_id: Уникальный ID позиции
            
        Returns:
            SolanaClient для этой позиции или None если нет доступных RPC
        """
        if position_id in self.reserved_rpcs:
            logger.warning(f"⚠️ Позиция {position_id} уже имеет зарезервированный RPC")
            return self.reserved_rpcs[position_id][1]
        
        if not self.available_rpcs:
            logger.warning(f"❌ Нет доступных RPC для позиции {position_id}")
            logger.warning("🔄 Используем fallback - общий MultiRPC")
            return None
        
        # Берем первый доступный RPC
        rpc_index, endpoint = self.available_rpcs.pop(0)
        
        # Создаем dedicated client
        try:
            single_rpc_provider = MultiRpcProvider([endpoint])
            dedicated_client = SolanaClient(multi_rpc_provider=single_rpc_provider)
            
            # Сохраняем резервацию
            self.reserved_rpcs[position_id] = (rpc_index, dedicated_client)
            
            rpc_name = self._get_rpc_name(endpoint)
            logger.info(f"🎯 RPC #{rpc_index+1} ({rpc_name}) зарезервирован для позиции {position_id}")
            logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")
            
            return dedicated_client
            
        except Exception as e:
            logger.error(f"❌ Ошибка создания dedicated client для позиции {position_id}: {e}")
            # Возвращаем RPC обратно в пул
            self.available_rpcs.insert(0, (rpc_index, endpoint))
            return None
    
    def get_rpc_for_position(self, position_id: str) -> Optional[SolanaClient]:
        """
        Получает зарезервированный RPC для позиции.
        
        Args:
            position_id: ID позиции
            
        Returns:
            SolanaClient для этой позиции или None
        """
        if position_id in self.reserved_rpcs:
            return self.reserved_rpcs[position_id][1]
        
        logger.warning(f"⚠️ Позиция {position_id} не имеет зарезервированного RPC")
        return None

    def reserve_specific_rpc_for_position(self, position_id: str, rpc_client: SolanaClient) -> bool:
        """
        Резервирует КОНКРЕТНЫЙ RPC клиент для торговой позиции.

        Args:
            position_id: Уникальный ID позиции
            rpc_client: Конкретный RPC клиент который нужно зарезервировать

        Returns:
            True если резервирование успешно, False если RPC недоступен
        """
        if position_id in self.reserved_rpcs:
            logger.warning(f"⚠️ Позиция {position_id} уже имеет зарезервированный RPC")
            return False

        # Сначала ищем этот RPC в доступных
        for i, (rpc_index, endpoint) in enumerate(self.available_rpcs):
            # Проверяем, соответствует ли endpoint RPC клиенту
            if self._is_same_rpc_client(rpc_client, endpoint):
                # Удаляем из доступных
                self.available_rpcs.pop(i)

                # Сохраняем резервацию с оригинальным клиентом
                self.reserved_rpcs[position_id] = (rpc_index, rpc_client)

                rpc_name = self._get_rpc_name(endpoint)
                logger.info(f"🎯 RPC #{rpc_index+1} ({rpc_name}) из покупки зарезервирован для позиции {position_id}")
                logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")

                return True

        # Если RPC не найден в доступных, но клиент валидный - резервируем его напрямую
        if rpc_client and hasattr(rpc_client, 'multi_rpc_provider'):
            # Создаем фиктивный индекс для этого RPC
            fake_index = 999  # Специальный индекс для "занятых" RPC
            self.reserved_rpcs[position_id] = (fake_index, rpc_client)

            logger.info(f"🎯 RPC из покупки зарезервирован напрямую для позиции {position_id} (был занят)")
            logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")

            return True

        logger.warning(f"⚠️ RPC клиент из покупки не найден в доступных для позиции {position_id}")
        return False

    def _is_same_rpc_client(self, rpc_client: SolanaClient, endpoint: Dict) -> bool:
        """
        Проверяет, соответствует ли RPC клиент данному endpoint.

        Args:
            rpc_client: RPC клиент для проверки
            endpoint: Endpoint для сравнения

        Returns:
            True если это тот же RPC
        """
        try:
            # Получаем URL из RPC клиента
            if hasattr(rpc_client, 'multi_rpc_provider') and rpc_client.multi_rpc_provider:
                client_endpoints = rpc_client.multi_rpc_provider.endpoints
                if client_endpoints:
                    client_url = client_endpoints[0].get('url', '')
                    endpoint_url = endpoint.get('url', '')
                    return client_url == endpoint_url
            return False
        except Exception as e:
            logger.warning(f"⚠️ Ошибка при сравнении RPC клиентов: {e}")
            return False

    def release_rpc(self, position_id: str) -> bool:
        """
        Освобождает RPC после закрытия позиции.

        Args:
            position_id: ID позиции

        Returns:
            True если RPC был освобожден, False если позиция не найдена
        """
        if position_id not in self.reserved_rpcs:
            logger.warning(f"⚠️ Попытка освободить RPC для несуществующей позиции {position_id}")
            return False

        rpc_index, dedicated_client = self.reserved_rpcs.pop(position_id)

        # 🔧 ИСПРАВЛЕНО: Проверяем fake_index для "занятых" RPC
        if rpc_index == 999:  # Fake index для занятых RPC
            logger.info(f"🔄 Освобожден занятый RPC для позиции {position_id}")
            logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")
        else:
            # Возвращаем RPC в пул доступных только если это реальный индекс
            if rpc_index < len(self.all_endpoints):
                endpoint = self.all_endpoints[rpc_index]
                self.available_rpcs.append((rpc_index, endpoint))

                rpc_name = self._get_rpc_name(endpoint)
                logger.info(f"🔄 RPC #{rpc_index+1} ({rpc_name}) освобожден от позиции {position_id}")
                logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")
            else:
                logger.warning(f"⚠️ Неверный индекс RPC {rpc_index} для позиции {position_id}")
                logger.info(f"📊 Статус: {len(self.reserved_rpcs)} активных, {len(self.available_rpcs)} доступных")

        # Закрываем соединения dedicated client
        try:
            # MultiRpcProvider автоматически закроет соединения при удалении
            del dedicated_client
        except Exception as e:
            logger.warning(f"⚠️ Ошибка при закрытии dedicated client: {e}")

        return True
    
    def get_discovery_rpc_provider(self) -> MultiRpcProvider:
        """
        Получает MultiRpcProvider для поиска новых токенов.
        
        Returns:
            MultiRpcProvider с RPC endpoints для поиска токенов
        """
        discovery_endpoints = [endpoint for _, endpoint in self.discovery_rpcs]
        return MultiRpcProvider(discovery_endpoints)
    
    def get_status(self) -> Dict:
        """
        Получает текущий статус менеджера.
        
        Returns:
            Словарь со статистикой
        """
        return {
            "total_rpcs": len(self.all_endpoints),
            "available_for_positions": len(self.available_rpcs),
            "reserved_for_positions": len(self.reserved_rpcs),
            "reserved_for_discovery": len(self.discovery_rpcs),
            "active_positions": list(self.reserved_rpcs.keys())
        }
    
    def can_handle_new_position(self) -> bool:
        """
        Проверяет, можно ли создать новую позицию.
        
        Returns:
            True если есть доступные RPC для новой позиции
        """
        return len(self.available_rpcs) > 0
    
    def get_max_positions(self) -> int:
        """
        Возвращает максимальное количество одновременных позиций.
        
        Returns:
            Максимальное количество позиций с dedicated RPC
        """
        return len(self.all_endpoints) - self.reserve_rpcs_for_discovery
