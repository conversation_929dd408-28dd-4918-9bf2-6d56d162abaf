"""
Solana client abstraction for blockchain operations.
"""

import asyncio
import json
from typing import Any

import aiohttp
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Processed
from solana.rpc.types import TxOpts
from solders.compute_budget import set_compute_unit_limit, set_compute_unit_price
from solders.hash import Hash
from solders.instruction import Instruction
from solders.keypair import Keypair
from solders.message import Message
from solders.pubkey import Pubkey
from solders.transaction import Transaction

from src.utils.logger import get_logger

logger = get_logger(__name__)


from src.core.multi_rpc import MultiRpcProvider

class SolanaClient:
    """Abstraction for Solana RPC client operations, with optional multi-RPC support."""

    def __init__(self, rpc_endpoint: str = None, multi_rpc_provider: MultiRpcProvider = None):
        """
        Args:
            rpc_endpoint: URL of the Solana RPC endpoint (legacy/single)
            multi_rpc_provider: MultiRpcProvider instance (preferred for multi-RPC)
        """
        self.multi_rpc_provider = multi_rpc_provider
        self._current_rpc_idx = None  # Для отслеживания текущего RPC индекса
        if multi_rpc_provider:
            self.rpc_endpoint = multi_rpc_provider.next_rpc()
        else:
            self.rpc_endpoint = rpc_endpoint
        self._client = None
        self._cached_blockhash: Hash | None = None
        self._blockhash_lock = asyncio.Lock()
        self._blockhash_updater_task = asyncio.create_task(self.start_blockhash_updater())

    async def start_blockhash_updater(self, interval: float = 5.0):
        """Start background task to update recent blockhash."""
        while True:
            try:
                blockhash = await self.get_latest_blockhash()
                async with self._blockhash_lock:
                    self._cached_blockhash = blockhash
            except Exception as e:
                logger.warning(f"Blockhash fetch failed: {e!s}")
            finally:
                await asyncio.sleep(interval)

    async def get_cached_blockhash(self) -> Hash:
        """Return the most recently cached blockhash."""
        async with self._blockhash_lock:
            if self._cached_blockhash is None:
                raise RuntimeError("No cached blockhash available yet")
            return self._cached_blockhash

    async def get_client(self) -> AsyncClient:
        """Get or create the AsyncClient instance from the RPC pool."""
        if self.multi_rpc_provider:
            # Получаем доступный клиент из пула
            client, idx = self.multi_rpc_provider.get_next_client()

            # Если все RPC заняты, ждем и пробуем снова
            if client is None:
                await asyncio.sleep(0.05)  # Короткая задержка
                client, idx = self.multi_rpc_provider.get_next_client()

            if client is None:
                raise Exception("All RPC endpoints are busy or failed")

            self._current_rpc_idx = idx
            self.rpc_endpoint = self.multi_rpc_provider.endpoints[idx]["url"]
            return client
        elif self._client is None:
            self._client = AsyncClient(self.rpc_endpoint)
        return self._client

    async def post_rpc(self, body: dict[str, Any], max_attempts: int = 3) -> dict[str, Any] | None:
        """
        Send a raw RPC request to the Solana node with failover support.

        Args:
            body: JSON-RPC request body.
            max_attempts: How many endpoints to try (if multi-RPC)

        Returns:
            Optional[Dict[str, Any]]: Parsed JSON response, or None if the request fails.
        """
        attempts = 0
        last_exc = None
        while attempts < max_attempts:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.rpc_endpoint,
                        json=body,
                        timeout=aiohttp.ClientTimeout(10),
                    ) as response:
                        response.raise_for_status()
                        return await response.json()
            except aiohttp.ClientError as e:
                error_type = "connection_error"
                if "429" in str(e) or "rate limit" in str(e).lower():
                    error_type = "rate_limit"
                elif "timeout" in str(e).lower():
                    error_type = "timeout"

                logger.error(f"RPC request failed: {e!s} (endpoint: {self.rpc_endpoint})", exc_info=True)
                last_exc = e
                if self.multi_rpc_provider and self._current_rpc_idx is not None:
                    # Отмечаем RPC как проблемный, но НЕ переключаемся автоматически
                    self.multi_rpc_provider.mark_failed_by_idx(self._current_rpc_idx, error_type)
                    attempts += 1

                    # Пробуем получить другой доступный RPC только для retry
                    if attempts < max_attempts:
                        client, idx = self.multi_rpc_provider.get_next_client()
                        if client is not None:
                            self._current_rpc_idx = idx
                            self.rpc_endpoint = self.multi_rpc_provider.endpoints[idx]["url"]
                        else:
                            # Все RPC заняты - прерываем попытки
                            break
                else:
                    break
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode RPC response: {e!s}", exc_info=True)
                last_exc = e
                if self.multi_rpc_provider and self._current_rpc_idx is not None:
                    self.multi_rpc_provider.mark_failed_by_idx(self._current_rpc_idx, "json_decode_error")
                break
        logger.error(f"All RPC endpoints failed after {attempts} attempts")
        if last_exc:
            raise last_exc
        return None

    async def close(self):
        """Close the client connection and stop the blockhash updater."""
        if self._blockhash_updater_task:
            self._blockhash_updater_task.cancel()
            try:
                await self._blockhash_updater_task
            except asyncio.CancelledError:
                pass

        if self._client:
            await self._client.close()
            self._client = None

    async def get_health(self) -> str | None:
        body = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getHealth",
        }
        result = await self.post_rpc(body)
        if result and "result" in result:
            return result["result"]
        return None

    async def get_account_info(self, pubkey: Pubkey) -> dict[str, Any]:
        """Get account info from the blockchain.

        Args:
            pubkey: Public key of the account

        Returns:
            Account info response

        Raises:
            ValueError: If account doesn't exist or has no data
        """
        client = await self.get_client()
        response = await client.get_account_info(pubkey, encoding="base64") # base64 encoding for account data by default
        if not response.value:
            raise ValueError(f"Account {pubkey} not found")
        return response.value

    async def get_token_account_balance(self, token_account: Pubkey) -> int:
        """Get token balance for an account.

        Args:
            token_account: Token account address

        Returns:
            Token balance as integer

        Raises:
            ValueError: If token account doesn't exist
        """
        client = await self.get_client()
        try:
            response = await client.get_token_account_balance(token_account)
            if response.value:
                return int(response.value.amount)
            else:
                # Account exists but has no balance data (should not happen for valid token accounts)
                return 0
        except Exception as e:
            # Check if it's the "could not find account" error
            if "could not find account" in str(e).lower():
                raise ValueError(f"Token account {token_account} not found - may not be created yet or already closed")
            else:
                # Re-raise other errors
                raise

    async def get_latest_blockhash(self) -> Hash:
        """Get the latest blockhash.

        Returns:
            Recent blockhash as string
        """
        client = await self.get_client()
        response = await client.get_latest_blockhash(commitment="processed")
        return response.value.blockhash

    async def build_and_send_transaction(
        self,
        instructions: list[Instruction],
        signer_keypair: Keypair,
        skip_preflight: bool = True,
        max_retries: int = 3,
        priority_fee: int | None = None,
    ) -> str:
        """
        Send a transaction with optional priority fee.

        Args:
            instructions: List of instructions to include in the transaction.
            skip_preflight: Whether to skip preflight checks.
            max_retries: Maximum number of retry attempts.
            priority_fee: Optional priority fee in microlamports.

        Returns:
            Transaction signature.
        """
        client = await self.get_client()

        logger.info(
            f"Priority fee in microlamports: {priority_fee if priority_fee else 0}"
        )

        # Add priority fee instructions if applicable
        if priority_fee is not None:
            fee_instructions = [
                set_compute_unit_limit(72_000),  # Default compute unit limit
                set_compute_unit_price(priority_fee),
            ]
            instructions = fee_instructions + instructions

        recent_blockhash = await self.get_cached_blockhash()
        message = Message(instructions, signer_keypair.pubkey())
        transaction = Transaction([signer_keypair], message, recent_blockhash)

        for attempt in range(max_retries):
            try:
                tx_opts = TxOpts(
                    skip_preflight=skip_preflight, preflight_commitment=Processed
                )
                response = await client.send_transaction(transaction, tx_opts)
                return response.value

            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(
                        f"Failed to send transaction after {max_retries} attempts"
                    )
                    raise

                wait_time = 2**attempt
                logger.warning(
                    f"Transaction attempt {attempt + 1} failed: {e!s}, retrying in {wait_time}s"
                )
                await asyncio.sleep(wait_time)

    async def confirm_transaction(
        self, signature: str, commitment: str = "confirmed", timeout: int = 5
    ) -> bool:
        """Wait for transaction confirmation.

        Args:
            signature: Transaction signature
            commitment: Confirmation commitment level
            timeout: Maximum time to wait for confirmation (seconds)

        Returns:
            Whether transaction was confirmed
        """
        client = await self.get_client()
        try:
            # Используем asyncio.wait_for для ограничения времени ожидания
            await asyncio.wait_for(
                client.confirm_transaction(signature, commitment=commitment, sleep_seconds=1),
                timeout=timeout
            )
            return True
        except asyncio.TimeoutError:
            logger.error(f"Transaction confirmation timeout after {timeout}s: {signature}")
            return False
        except Exception as e:
            logger.error(f"Failed to confirm transaction {signature}: {e!s}")
            return False

    async def get_transaction(self, signature: str, encoding: str = "jsonParsed", commitment: str = "confirmed", max_supported_transaction_version: int = 0):
        """Get transaction details.

        Args:
            signature: Transaction signature
            encoding: Response encoding format
            commitment: Commitment level
            max_supported_transaction_version: Maximum transaction version

        Returns:
            Transaction details
        """
        client = await self.get_client()
        return await client.get_transaction(
            signature,
            encoding=encoding,
            commitment=commitment,
            max_supported_transaction_version=max_supported_transaction_version
        )

    async def post_rpc(self, body: dict[str, Any]) -> dict[str, Any] | None:
        """
        Send a raw RPC request to the Solana node.

        Args:
            body: JSON-RPC request body.

        Returns:
            Optional[Dict[str, Any]]: Parsed JSON response, or None if the request fails.
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.rpc_endpoint,
                    json=body,
                    timeout=aiohttp.ClientTimeout(10),  # 10-second timeout
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"RPC request failed: {e!s}", exc_info=True)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode RPC response: {e!s}", exc_info=True)
            return None
