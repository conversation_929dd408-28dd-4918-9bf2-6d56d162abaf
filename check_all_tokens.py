#!/usr/bin/env python3
"""
Скрипт для проверки всех токенов в кошельке
"""

import asyncio
import os
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient

load_dotenv()

async def check_all_tokens():
    """Проверяет все токены в кошельке"""
    
    # Получаем приватный ключ из .env
    private_key_str = os.getenv("SOLANA_PRIVATE_KEY")
    if not private_key_str:
        print("❌ SOLANA_PRIVATE_KEY не найден в .env файле!")
        return
    
    # Создаем кошелек
    try:
        private_key_bytes = base58.b58decode(private_key_str)
        keypair = Keypair.from_bytes(private_key_bytes)
        wallet_address = str(keypair.pubkey())
        print(f"🔑 Адрес кошелька: {wallet_address}")
    except Exception as e:
        print(f"❌ Ошибка создания кошелька: {e}")
        return
    
    # Подключаемся к RPC
    rpc_endpoint = os.getenv("SOLANA_NODE_RPC_ENDPOINT", "https://api.mainnet-beta.solana.com")
    print(f"🌐 RPC endpoint: {rpc_endpoint}")
    
    try:
        async with AsyncClient(rpc_endpoint) as client:
            # Получаем все token accounts
            response = await client.get_token_accounts_by_owner(
                keypair.pubkey(),
                {"programId": Pubkey.from_string("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")}
            )
            
            if response.value:
                print(f"\n💰 Найдено {len(response.value)} токенов в кошельке:")
                print("=" * 80)
                
                total_tokens = 0
                for i, account in enumerate(response.value, 1):
                    try:
                        account_info = account.account
                        parsed_data = account_info.data
                        
                        # Получаем информацию о токене
                        token_account_info = await client.get_token_account_balance(account.pubkey)
                        
                        if token_account_info.value:
                            balance = token_account_info.value.amount
                            decimals = token_account_info.value.decimals
                            balance_decimal = int(balance) / (10 ** decimals)
                            
                            if balance_decimal > 0:
                                # Получаем mint address
                                account_data = await client.get_account_info(account.pubkey)
                                if account_data.value and account_data.value.data:
                                    # Mint address находится в первых 32 байтах данных аккаунта
                                    mint_bytes = account_data.value.data[:32]
                                    mint_address = str(Pubkey(mint_bytes))
                                    
                                    print(f"{i:2d}. 🪙 Mint: {mint_address}")
                                    print(f"    💰 Баланс: {balance_decimal:,.6f} токенов")
                                    print(f"    📍 Account: {account.pubkey}")
                                    print(f"    🔢 Raw amount: {balance}")
                                    print(f"    📊 Decimals: {decimals}")
                                    
                                    # Проверяем известные токены
                                    known_tokens = {
                                        "Hy3cHuW6fQLk1J8uEBKd15SpkYPTZpgTi2ies4Bwpump": "Chewy (упал -60%)",
                                        "HuohZe48dKV7G8Q3FpzvtyWtvtFTdXzqSo5Zr6fVMof4": "Tokake (упал -48%)",
                                        "7KnpHuPZsvqqvkmpPHsNVobKESBN1FVqfVj2r2cqTxTQ": "Obama (упал -48%)"
                                    }
                                    
                                    if mint_address in known_tokens:
                                        print(f"    🚨 {known_tokens[mint_address]}")
                                    
                                    print("-" * 80)
                                    total_tokens += 1
                                    
                    except Exception as e:
                        print(f"❌ Ошибка обработки токена {i}: {e}")
                        continue
                
                print(f"\n📊 Итого токенов с балансом > 0: {total_tokens}")
                
                if total_tokens > 0:
                    print("\n⚠️  РЕКОМЕНДАЦИИ:")
                    print("1. Токены с убытками можно держать для возможного восстановления")
                    print("2. Или продать вручную если нужны средства")
                    print("3. Исправленный бот теперь будет корректно продавать токены")
                
            else:
                print("✅ Токенов в кошельке не найдено")
                
    except Exception as e:
        print(f"❌ Ошибка подключения к RPC: {e}")

if __name__ == "__main__":
    asyncio.run(check_all_tokens())
