#!/bin/bash

# Скрипт проверки статуса pump.fun бота
# Использование: ./status_bot.sh

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Функция логирования
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# Функция форматирования времени
format_time() {
    local seconds=$1
    local days=$((seconds / 86400))
    local hours=$(((seconds % 86400) / 3600))
    local minutes=$(((seconds % 3600) / 60))
    local secs=$((seconds % 60))
    
    if [ $days -gt 0 ]; then
        echo "${days}d ${hours}h ${minutes}m ${secs}s"
    elif [ $hours -gt 0 ]; then
        echo "${hours}h ${minutes}m ${secs}s"
    elif [ $minutes -gt 0 ]; then
        echo "${minutes}m ${secs}s"
    else
        echo "${secs}s"
    fi
}

# Функция форматирования размера
format_size() {
    local size=$1
    if [ $size -gt 1073741824 ]; then
        echo "$(echo "scale=1; $size/1073741824" | bc)GB"
    elif [ $size -gt 1048576 ]; then
        echo "$(echo "scale=1; $size/1048576" | bc)MB"
    elif [ $size -gt 1024 ]; then
        echo "$(echo "scale=1; $size/1024" | bc)KB"
    else
        echo "${size}B"
    fi
}

echo ""
header "СТАТУС PUMP.FUN БОТА"
echo ""

# 1. Проверка процессов
header "Процессы"
BOT_PIDS=$(pgrep -f "bot_runner.py" 2>/dev/null || true)

if [ -z "$BOT_PIDS" ]; then
    error "Бот не запущен"
    echo ""
else
    success "Бот активен"
    echo ""
    echo "Активные процессы:"
    echo "$BOT_PIDS" | while read pid; do
        if [ ! -z "$pid" ]; then
            # Получение информации о процессе
            if ps -p $pid > /dev/null 2>&1; then
                start_time=$(ps -o lstart= -p $pid 2>/dev/null | xargs)
                cpu_usage=$(ps -o %cpu= -p $pid 2>/dev/null | xargs)
                mem_usage=$(ps -o %mem= -p $pid 2>/dev/null | xargs)
                rss=$(ps -o rss= -p $pid 2>/dev/null | xargs)
                
                # Вычисление времени работы
                start_epoch=$(date -d "$start_time" +%s 2>/dev/null || echo "0")
                current_epoch=$(date +%s)
                uptime=$((current_epoch - start_epoch))
                
                echo "  PID: $pid"
                echo "    Время работы: $(format_time $uptime)"
                echo "    CPU: ${cpu_usage}%"
                echo "    RAM: ${mem_usage}% ($(format_size $((rss * 1024))))"
                echo "    Запущен: $start_time"
                echo ""
            fi
        fi
    done
fi

# 2. Проверка PID файла
header "PID Файл"
PID_FILE="bot.pid"
if [ -f "$PID_FILE" ]; then
    stored_pid=$(cat "$PID_FILE")
    if kill -0 $stored_pid 2>/dev/null; then
        success "PID файл корректен: $stored_pid"
    else
        warning "PID файл содержит неактивный процесс: $stored_pid"
    fi
else
    log "PID файл не найден"
fi
echo ""

# 3. Проверка логов
header "Логи"
if [ -d "logs" ]; then
    log_count=$(find logs -name "*.log" -type f 2>/dev/null | wc -l)
    if [ $log_count -gt 0 ]; then
        success "Найдено $log_count лог-файлов"
        echo ""
        
        # Последние логи
        echo "Последние лог-файлы:"
        find logs -name "*.log" -type f -printf "%T@ %p\n" 2>/dev/null | sort -n | tail -5 | while read timestamp file; do
            size=$(stat -c%s "$file" 2>/dev/null || echo "0")
            mod_time=$(date -d "@$timestamp" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "unknown")
            lines=$(wc -l < "$file" 2>/dev/null || echo "?")
            echo "  $(basename "$file"): $(format_size $size) ($lines строк) - $mod_time"
        done
        echo ""
        
        # Проверка больших файлов
        large_logs=$(find logs -name "*.log" -size +100M 2>/dev/null || true)
        if [ ! -z "$large_logs" ]; then
            warning "Очень большие лог-файлы (>100MB):"
            echo "$large_logs" | while read file; do
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                echo "  $(basename "$file"): $(format_size $size)"
            done
            echo ""
        fi
        
        # Последние записи
        latest_log=$(find logs -name "bot-sniper-*.log" -type f -printf "%T@ %p\n" 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2)
        if [ ! -z "$latest_log" ] && [ -f "$latest_log" ]; then
            echo "Последние 3 записи из $(basename "$latest_log"):"
            tail -3 "$latest_log" 2>/dev/null | while read line; do
                echo "  $line"
            done
            echo ""
        fi
    else
        warning "Лог-файлы не найдены"
    fi
else
    error "Директория logs не найдена"
fi
echo ""

# 4. Проверка системных ресурсов
header "Системные ресурсы"

# CPU
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
echo "CPU загрузка: ${cpu_usage}%"

# RAM
mem_info=$(free -m | grep "Mem:")
total_mem=$(echo $mem_info | awk '{print $2}')
used_mem=$(echo $mem_info | awk '{print $3}')
mem_percent=$(echo "scale=1; $used_mem*100/$total_mem" | bc)
echo "RAM: ${used_mem}MB / ${total_mem}MB (${mem_percent}%)"

# Диск
disk_usage=$(df -h . | tail -1 | awk '{print $5}' | cut -d'%' -f1)
disk_avail=$(df -h . | tail -1 | awk '{print $4}')
echo "Диск: ${disk_usage}% использовано, ${disk_avail} свободно"

echo ""

# 5. Проверка сетевых соединений
header "Сетевые соединения"
if [ ! -z "$BOT_PIDS" ]; then
    for pid in $BOT_PIDS; do
        if [ ! -z "$pid" ] && kill -0 $pid 2>/dev/null; then
            connections=$(netstat -tnp 2>/dev/null | grep "$pid/" | wc -l)
            echo "PID $pid: $connections активных соединений"
        fi
    done
else
    log "Бот не запущен - соединения не проверяются"
fi

echo ""

# 6. Рекомендации
header "Рекомендации"
if [ -z "$BOT_PIDS" ]; then
    echo "• Для запуска бота: ./start_bot.sh"
else
    echo "• Для остановки бота: ./stop_bot.sh"
    echo "• Для просмотра логов: tail -f logs/bot-sniper-*.log"
    echo "• Для мониторинга: watch -n 5 './status_bot.sh'"
    
    # Проверка времени работы
    if [ ! -z "$uptime" ] && [ $uptime -gt 3600 ]; then
        echo "• Бот работает больше часа - рекомендуется периодическая проверка логов"
    fi
fi

echo ""
