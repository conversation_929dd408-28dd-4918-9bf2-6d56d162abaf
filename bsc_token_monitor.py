#!/usr/bin/env python3
"""
🥞 BSC PANCAKESWAP TOKEN MONITOR
Мониторинг новых токенов на PancakeSwap (аналог pump.fun для BSC)

Отслеживает:
1. Создание новых пулов ликвидности
2. Первые покупки токенов
3. Анализ потенциала токенов
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import time

@dataclass
class BSCToken:
    """Информация о токене на BSC"""
    address: str
    name: str
    symbol: str
    decimals: int
    total_supply: float
    pair_address: str
    bnb_reserve: float
    token_reserve: float
    price_bnb: float
    price_usd: float
    market_cap: float
    liquidity_usd: float
    created_at: datetime
    creator: str

class BSCTokenMonitor:
    """
    🔍 МОНИТОР НОВЫХ ТОКЕНОВ НА BSC
    
    Аналог pump.fun мониторинга для PancakeSwap
    """
    
    def __init__(self):
        # BSC RPC endpoints
        self.rpc_endpoints = [
            "https://bsc-dataseed1.binance.org/",
            "https://bsc-dataseed2.binance.org/",
            "https://bsc-dataseed.ninicoin.io/"
        ]
        
        # PancakeSwap контракты
        self.pancake_factory = "0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73"
        self.pancake_router = "0x10ED43C718714eb63d5aA57B78B54704E256024E"
        self.wbnb_address = "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c"
        
        # API ключи (в реальном боте из .env)
        self.bscscan_api_key = "YourBSCScanAPIKey"
        self.moralis_api_key = "YourMoralisAPIKey"
        
        # Фильтры токенов
        self.min_liquidity_bnb = 1.0  # Минимум 1 BNB ликвидности
        self.max_age_minutes = 60     # Токены не старше 1 часа
        
        # Кэш обработанных токенов
        self.processed_tokens = set()
        
    async def get_latest_pairs_from_dexscreener(self) -> List[Dict]:
        """Получить последние пары с DexScreener"""
        try:
            url = "https://api.dexscreener.com/latest/dex/pairs/bsc"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    data = await response.json()
                    
                    if 'pairs' in data:
                        # Фильтруем только PancakeSwap пары
                        pancake_pairs = [
                            pair for pair in data['pairs'] 
                            if pair.get('dexId') == 'pancakeswap'
                        ]
                        
                        # Сортируем по времени создания (новые первыми)
                        pancake_pairs.sort(
                            key=lambda x: x.get('pairCreatedAt', 0), 
                            reverse=True
                        )
                        
                        return pancake_pairs[:50]  # Топ-50 новых пар
        except Exception as e:
            print(f"❌ Error fetching DexScreener data: {e}")
        
        return []
    
    async def get_token_info_from_bscscan(self, token_address: str) -> Optional[Dict]:
        """Получить информацию о токене с BSCScan"""
        try:
            # Получаем базовую информацию о токене
            url = f"https://api.bscscan.com/api"
            params = {
                'module': 'token',
                'action': 'tokeninfo',
                'contractaddress': token_address,
                'apikey': self.bscscan_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    if data.get('status') == '1' and data.get('result'):
                        token_info = data['result'][0]
                        return {
                            'name': token_info.get('tokenName', 'Unknown'),
                            'symbol': token_info.get('tokenSymbol', 'UNK'),
                            'decimals': int(token_info.get('divisor', 18)),
                            'total_supply': float(token_info.get('totalSupply', 0))
                        }
        except Exception as e:
            print(f"❌ Error fetching token info: {e}")
        
        return None
    
    async def analyze_token_potential(self, token: BSCToken) -> Dict[str, any]:
        """Анализ потенциала токена (аналог pump.fun фильтров)"""
        analysis = {
            'score': 0,
            'signals': [],
            'warnings': [],
            'recommendation': 'SKIP'
        }
        
        # 1. Проверка ликвидности
        if token.liquidity_usd >= 1000:  # $1000+ ликвидности
            analysis['score'] += 20
            analysis['signals'].append(f"Good liquidity: ${token.liquidity_usd:,.0f}")
        else:
            analysis['warnings'].append(f"Low liquidity: ${token.liquidity_usd:,.0f}")
        
        # 2. Проверка возраста токена
        age_minutes = (datetime.now() - token.created_at).total_seconds() / 60
        if age_minutes <= 30:  # Очень новый токен
            analysis['score'] += 30
            analysis['signals'].append(f"Very fresh: {age_minutes:.1f}m old")
        elif age_minutes <= 60:  # Новый токен
            analysis['score'] += 15
            analysis['signals'].append(f"Fresh: {age_minutes:.1f}m old")
        
        # 3. Проверка market cap
        if 1000 <= token.market_cap <= 100000:  # $1K-$100K market cap
            analysis['score'] += 25
            analysis['signals'].append(f"Good MC: ${token.market_cap:,.0f}")
        elif token.market_cap > 100000:
            analysis['warnings'].append(f"High MC: ${token.market_cap:,.0f}")
        
        # 4. Проверка цены
        if 0.000001 <= token.price_usd <= 0.01:  # Хорошая цена для роста
            analysis['score'] += 15
            analysis['signals'].append(f"Good price: ${token.price_usd:.8f}")
        
        # 5. Проверка имени токена (простая эвристика)
        suspicious_words = ['test', 'fake', 'scam', 'rug', 'honeypot']
        if any(word in token.name.lower() for word in suspicious_words):
            analysis['score'] -= 50
            analysis['warnings'].append("Suspicious name")
        
        # Финальная рекомендация
        if analysis['score'] >= 70:
            analysis['recommendation'] = 'BUY'
        elif analysis['score'] >= 40:
            analysis['recommendation'] = 'WATCH'
        else:
            analysis['recommendation'] = 'SKIP'
        
        return analysis
    
    async def process_new_token(self, pair_data: Dict) -> Optional[BSCToken]:
        """Обработка нового токена"""
        try:
            # Извлекаем данные из DexScreener
            token_address = pair_data['baseToken']['address']
            pair_address = pair_data['pairAddress']
            
            # Проверяем, не обрабатывали ли уже этот токен
            if token_address in self.processed_tokens:
                return None
            
            # Получаем дополнительную информацию о токене
            token_info = await self.get_token_info_from_bscscan(token_address)
            if not token_info:
                return None
            
            # Создаем объект токена
            token = BSCToken(
                address=token_address,
                name=token_info['name'],
                symbol=token_info['symbol'],
                decimals=token_info['decimals'],
                total_supply=token_info['total_supply'],
                pair_address=pair_address,
                bnb_reserve=float(pair_data.get('liquidity', {}).get('base', 0)),
                token_reserve=float(pair_data.get('liquidity', {}).get('quote', 0)),
                price_bnb=float(pair_data.get('priceNative', 0)),
                price_usd=float(pair_data.get('priceUsd', 0)),
                market_cap=float(pair_data.get('marketCap', 0)),
                liquidity_usd=float(pair_data.get('liquidity', {}).get('usd', 0)),
                created_at=datetime.fromtimestamp(pair_data.get('pairCreatedAt', 0) / 1000),
                creator=pair_data.get('pairCreatedBy', 'Unknown')
            )
            
            # Добавляем в обработанные
            self.processed_tokens.add(token_address)
            
            return token
            
        except Exception as e:
            print(f"❌ Error processing token: {e}")
            return None
    
    async def monitor_new_tokens(self):
        """Основной цикл мониторинга новых токенов"""
        print("🥞 STARTING BSC TOKEN MONITOR")
        print("Monitoring PancakeSwap for new tokens...")
        print("=" * 60)
        
        cycle_count = 0
        
        while True:
            try:
                cycle_count += 1
                print(f"\n🔄 MONITORING CYCLE #{cycle_count}")
                
                # Получаем последние пары
                pairs = await self.get_latest_pairs_from_dexscreener()
                
                if not pairs:
                    print("❌ No pairs data received")
                    await asyncio.sleep(30)
                    continue
                
                new_tokens_found = 0
                
                for pair_data in pairs:
                    # Обрабатываем только новые токены
                    token = await self.process_new_token(pair_data)
                    
                    if token:
                        new_tokens_found += 1
                        
                        # Анализируем потенциал токена
                        analysis = await self.analyze_token_potential(token)
                        
                        # Выводим информацию о токене
                        print(f"\n🎯 NEW TOKEN FOUND:")
                        print(f"   Name: {token.name} ({token.symbol})")
                        print(f"   Address: {token.address}")
                        print(f"   Price: ${token.price_usd:.8f}")
                        print(f"   Market Cap: ${token.market_cap:,.0f}")
                        print(f"   Liquidity: ${token.liquidity_usd:,.0f}")
                        print(f"   Age: {(datetime.now() - token.created_at).total_seconds() / 60:.1f}m")
                        print(f"   Score: {analysis['score']}/100")
                        print(f"   Recommendation: {analysis['recommendation']}")
                        
                        if analysis['signals']:
                            print(f"   ✅ Signals: {', '.join(analysis['signals'])}")
                        if analysis['warnings']:
                            print(f"   ⚠️ Warnings: {', '.join(analysis['warnings'])}")
                        
                        # Если токен перспективный, выделяем его
                        if analysis['recommendation'] == 'BUY':
                            print(f"   🚀 POTENTIAL BUY OPPORTUNITY!")
                
                print(f"\n📊 Cycle summary: {new_tokens_found} new tokens processed")
                
                # Пауза между циклами
                await asyncio.sleep(60)  # Проверяем каждую минуту
                
            except Exception as e:
                print(f"❌ Error in monitoring cycle: {e}")
                await asyncio.sleep(30)

# Демо функция
async def demo_bsc_monitor():
    """Демонстрация BSC токен монитора"""
    monitor = BSCTokenMonitor()
    
    # Запускаем мониторинг на 5 минут для демо
    try:
        await asyncio.wait_for(monitor.monitor_new_tokens(), timeout=300)
    except asyncio.TimeoutError:
        print("\n🏁 Demo completed!")

if __name__ == "__main__":
    print("🥞 BSC PANCAKESWAP TOKEN MONITOR DEMO")
    print("Мониторинг новых токенов на PancakeSwap (аналог pump.fun)")
    print("\nЗапуск через 3 секунды...")
    
    import time
    time.sleep(3)
    
    # Запуск демо
    asyncio.run(demo_bsc_monitor())
