#!/bin/bash
# 🔧 ПОЛНАЯ ПРОВЕРКА БОТА ПЕРЕД ЗАПУСКОМ
# Автор: Pump.fun Trading Bot
# Дата: 2025-07-25

echo "🚀 ПОЛНАЯ ПРОВЕРКА БОТА ПЕРЕД ЗАПУСКОМ"
echo "======================================"

# 1. 📊 ПРОВЕРКА СТАТУСА СИСТЕМЫ
echo ""
echo "1. 📊 ПРОВЕРКА СТАТУСА СИСТЕМЫ:"
echo "-------------------------------"

echo "🔍 Статус бота:"
./status_bot.sh

echo ""
echo "💾 Свободное место на диске:"
df -h .

echo ""
echo "🖥️ Использование RAM:"
free -h

echo ""
echo "⚡ Загрузка CPU (топ-5 процессов):"
top -bn1 | head -10

# 2. 🔍 ПРОВЕРКА КОНФИГУРАЦИИ
echo ""
echo "2. 🔍 ПРОВЕРКА КОНФИГУРАЦИИ:"
echo "----------------------------"

echo "📝 Проверка синтаксиса YAML и настроек:"
python3 -c "
import yaml
try:
    with open('bots/bot-sniper-2-logs.yaml', 'r') as f:
        config = yaml.safe_load(f)
    print('✅ YAML синтаксис корректный')
    print(f'💰 Buy amount: {config[\"trade\"][\"buy_amount\"]} SOL')
    print(f'🛡️ Price drop threshold: {config[\"filters\"][\"max_price_drop_threshold\"]}')
    print(f'🛡️ Stability check: {config[\"filters\"][\"price_stability_check\"]}')
    print(f'🛡️ Min growth checks: {config[\"filters\"][\"min_growth_checks\"]}')
    print(f'📊 Take profit: {config[\"trade\"][\"take_profit_percentage\"]}')
    print(f'📊 Stop loss: {config[\"trade\"][\"stop_loss_percentage\"]}')
except Exception as e:
    print(f'❌ Ошибка конфигурации: {e}')
"

echo ""
echo "🔑 Проверка переменных окружения:"
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()

required_vars = [
    'SOLANA_PRIVATE_KEY',
    'CHAINSTACK_RPC_ENDPOINT', 
    'ALCHEMY_RPC_ENDPOINT',
    'QUICKNODE_RPC_ENDPOINT',
    'HELIUS_RPC_ENDPOINT'
]

print('🔑 ПЕРЕМЕННЫЕ ОКРУЖЕНИЯ:')
for var in required_vars:
    value = os.getenv(var)
    if value:
        if 'PRIVATE_KEY' in var:
            print(f'✅ {var}: {"*" * 20}...{value[-10:]} (длина: {len(value)})')
        else:
            print(f'✅ {var}: {"*" * 30}...{value[-15:]}')
    else:
        print(f'❌ {var}: НЕ НАЙДЕНА')
"

# 3. 🛠️ ПРОВЕРКА КОДА
echo ""
echo "3. 🛠️ ПРОВЕРКА КОДА:"
echo "--------------------"

echo "🐍 Проверка синтаксиса Python:"
if python3 -m py_compile src/trading/trader.py; then
    echo "✅ trader.py - синтаксис корректный"
else
    echo "❌ trader.py - ошибка синтаксиса"
fi

if python3 -m py_compile src/trading/buyer.py; then
    echo "✅ buyer.py - синтаксис корректный"
else
    echo "❌ buyer.py - ошибка синтаксиса"
fi

if python3 -m py_compile src/trading/seller.py; then
    echo "✅ seller.py - синтаксис корректный"
else
    echo "❌ seller.py - ошибка синтаксиса"
fi

echo ""
echo "📦 Проверка импортов:"
python3 -c "
import sys
sys.path.append('.')
try:
    from src.trading.trader import PumpTrader
    from src.trading.buyer import TokenBuyer
    from src.trading.seller import TokenSeller
    print('✅ Все модули импортируются успешно')
except Exception as e:
    print(f'❌ Ошибка импорта: {e}')
"

# 4. 🌐 ПРОВЕРКА RPC ПОДКЛЮЧЕНИЙ
echo ""
echo "4. 🌐 ПРОВЕРКА RPC ПОДКЛЮЧЕНИЙ:"
echo "-------------------------------"

python3 -c "
import asyncio
import aiohttp
import os
from dotenv import load_dotenv
load_dotenv()

async def check_rpc(name, url):
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json={'method': 'getHealth', 'params': [], 'id': 1}, timeout=5) as resp:
                if resp.status == 200:
                    print(f'✅ {name}: OK')
                else:
                    print(f'❌ {name}: HTTP {resp.status}')
    except Exception as e:
        print(f'❌ {name}: {str(e)[:50]}...')

async def main():
    print('🌐 RPC ENDPOINTS:')
    endpoints = [
        ('Chainstack', os.getenv('CHAINSTACK_RPC_ENDPOINT')),
        ('Alchemy', os.getenv('ALCHEMY_RPC_ENDPOINT')),
        ('QuickNode', os.getenv('QUICKNODE_RPC_ENDPOINT')),
        ('Helius', os.getenv('HELIUS_RPC_ENDPOINT')),
        ('Solana.com', os.getenv('SOLANA_NODE_RPC_ENDPOINT'))
    ]
    
    tasks = [check_rpc(name, url) for name, url in endpoints if url]
    await asyncio.gather(*tasks)

asyncio.run(main())
"

# 5. 💰 ПРОВЕРКА БАЛАНСА КОШЕЛЬКА
echo ""
echo "5. 💰 ПРОВЕРКА БАЛАНСА КОШЕЛЬКА:"
echo "--------------------------------"

python3 -c "
import asyncio
import os
from dotenv import load_dotenv
from solana.rpc.async_api import AsyncClient
from solders.keypair import Keypair
load_dotenv()

async def check_balance():
    try:
        private_key = os.getenv('SOLANA_PRIVATE_KEY')
        if not private_key:
            print('❌ SOLANA_PRIVATE_KEY не найден')
            return
            
        # Проверяем длину ключа
        if len(private_key) != 88:
            print(f'⚠️ ВНИМАНИЕ: Длина приватного ключа {len(private_key)} символов (должно быть 88)')
            print('🧪 ТЕСТОВЫЙ РЕЖИМ: Ключ намеренно поврежден для тестирования')
            return
            
        keypair = Keypair.from_base58_string(private_key)
        
        client = AsyncClient('https://api.mainnet-beta.solana.com')
        balance = await client.get_balance(keypair.pubkey())
        sol_balance = balance.value / 1e9
        
        print(f'💰 БАЛАНС КОШЕЛЬКА:')
        print(f'Адрес: {keypair.pubkey()}')
        print(f'Баланс: {sol_balance:.6f} SOL')
        
        if sol_balance < 0.1:
            print('⚠️ ВНИМАНИЕ: Низкий баланс SOL!')
        else:
            print('✅ Баланс достаточный для торговли')
            
        await client.close()
    except Exception as e:
        print(f'❌ Ошибка проверки баланса: {e}')

asyncio.run(check_balance())
"

# 6. 📁 ПРОВЕРКА ФАЙЛОВОЙ СИСТЕМЫ
echo ""
echo "6. 📁 ПРОВЕРКА ФАЙЛОВОЙ СИСТЕМЫ:"
echo "--------------------------------"

echo "📁 Последние лог файлы:"
ls -lh logs/ | tail -5

echo ""
echo "🔐 Права доступа к скриптам:"
ls -la start_bot.sh stop_bot.sh status_bot.sh

echo ""
echo "💾 Размер логов и свободное место:"
echo "Размер папки logs:"
du -sh logs/
echo "Свободное место на диске:"
df -h . | grep -v Filesystem

# 7. 🎯 ПРОВЕРКА НОВЫХ НАСТРОЕК БЕЗОПАСНОСТИ
echo ""
echo "7. 🛡️ ПРОВЕРКА НАСТРОЕК БЕЗОПАСНОСТИ:"
echo "------------------------------------"

python3 -c "
import yaml
with open('bots/bot-sniper-2-logs.yaml', 'r') as f:
    config = yaml.safe_load(f)

print('🛡️ НАСТРОЙКИ БЕЗОПАСНОСТИ:')
filters = config['filters']
print(f'Max price drop: {filters.get(\"max_price_drop_threshold\", \"НЕ НАЙДЕНО\")} (должно быть 0.1)')
print(f'Stability check: {filters.get(\"price_stability_check\", \"НЕ НАЙДЕНО\")} (должно быть True)')
print(f'Stability period: {filters.get(\"min_stability_period\", \"НЕ НАЙДЕНО\")} сек (должно быть 15)')
print(f'Growth checks: {filters.get(\"min_growth_checks\", \"НЕ НАЙДЕНО\")} раз (должно быть 3)')

print(f'\\n💰 ТОРГОВЫЕ НАСТРОЙКИ:')
trade = config['trade']
print(f'Buy amount: {trade[\"buy_amount\"]} SOL')
print(f'Take profit: {trade[\"take_profit_percentage\"]}')
print(f'Stop loss: {trade[\"stop_loss_percentage\"]}')
print(f'Trailing activation: {trade[\"trailing_stop_activation\"]}')
"

# 8. 🚀 ФИНАЛЬНАЯ ПРОВЕРКА ГОТОВНОСТИ
echo ""
echo "8. 🚀 ФИНАЛЬНАЯ ПРОВЕРКА:"
echo "------------------------"

python3 -c "
print('🎯 СТАТУС ГОТОВНОСТИ:')
print('✅ Конфигурация проверена')
print('✅ Код скомпилирован')  
print('✅ RPC endpoints проверены')
print('✅ Новые настройки безопасности активны')
print('🧪 ТЕСТОВЫЙ РЕЖИМ: Приватный ключ поврежден')
print('📊 ГОТОВ К МОНИТОРИНГУ ПОПЫТОК ПОКУПКИ')
print('')
print('🚀 КОМАНДА ДЛЯ ЗАПУСКА:')
print('./start_bot.sh bots/bot-sniper-2-logs.yaml')
print('')
print('📊 КОМАНДА ДЛЯ МОНИТОРИНГА:')
print('tail -f logs/bot-sniper-2_*.log | grep -E \"INSTANT BUY|QUARANTINE.*BUY|buy_error\"')
"

echo ""
echo "======================================"
echo "🏁 ПРОВЕРКА ЗАВЕРШЕНА"
echo "======================================"

# ДОПОЛНИТЕЛЬНЫЕ ПОЛЕЗНЫЕ КОМАНДЫ:
echo ""
echo "📋 ДОПОЛНИТЕЛЬНЫЕ КОМАНДЫ:"
echo "--------------------------"
echo ""
echo "⚡ БЫСТРАЯ ПРОВЕРКА (одной командой):"
echo "bash quick_check.sh"
echo ""
echo "📊 МОНИТОРИНГ ПОПЫТОК ПОКУПКИ:"
echo "tail -f logs/bot-sniper-2_*.log | grep -E \"INSTANT BUY|QUARANTINE.*BUY|buy_error\""
echo ""
echo "📈 МОНИТОРИНГ AUDIT LOG:"
echo "tail -f logs/audit.log | jq ."
echo ""
echo "🔍 ПОИСК КОНКРЕТНОГО ТОКЕНА:"
echo "grep -r \"SYMBOL\" logs/"
echo ""
echo "📊 СТАТИСТИКА ПОПЫТОК ПОКУПКИ:"
echo "grep -c \"INSTANT BUY\" logs/bot-sniper-2_*.log"
echo ""
echo "🚀 ЗАПУСК БОТА:"
echo "./start_bot.sh bots/bot-sniper-2-logs.yaml"
echo ""
echo "🛑 ОСТАНОВКА БОТА:"
echo "./stop_bot.sh"
echo ""
echo "📊 СТАТУС БОТА:"
echo "./status_bot.sh"
