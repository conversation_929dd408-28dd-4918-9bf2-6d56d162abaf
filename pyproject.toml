[project]
name = "pump_bot"
version = "2.0"
description = "Trade tokens on pump.fun"
readme = "README.md"
requires-python = ">=3.9"

dependencies = [
    "base58>=2.1.1",
    "borsh-construct>=0.1.0",
    "construct>=2.10.67",
    "construct-typing>=0.5.2",
    "solana==0.36.6",
    "solders>=0.26.0",
    "websockets>=15.0",
    "python-dotenv>=1.0.1",
    "aiohttp>=3.11.13",
    "grpcio>=1.71.0",
    "grpcio-tools>=1.71.0",
    "protobuf>=5.29.4",
    "pyyaml>=6.0.2",
    "uvloop>=0.21.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.10.0"
]

[project.scripts]
pump_bot = "bot_runner:main"

[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

line-length = 88
indent-width = 4
target-version = "py311"

[tool.ruff.lint]
select = [
    "E", "F", "I", "UP", "N", "B", "A", "C4", "T10", "ARG", "PTH",
    "ANN",  # type annotations
    "S",    # security best practices
    "BLE",  # blind except statements
    "FBT",  # boolean trap parameters
    "C90",  # complexity metrics
    "TRY",  # exception handling best practices
    "SLF",  # private member access
    "TCH",  # type checking issues
    "RUF",  # Ruff-specific rules
    "ERA",  # eradicate commented-out code
    "PL",   # pylint conventions
]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
