#!/usr/bin/env python3
"""
Скрипт для удаления старых лог-файлов
Оставляет только 10 последних файлов каждого типа
"""

import os
from pathlib import Path
from datetime import datetime

def cleanup_old_files(directory, pattern, max_files=10):
    """Удаление старых файлов, оставляя только max_files последних."""
    files = list(directory.glob(pattern))
    if len(files) <= max_files:
        print(f"✅ {pattern}: {len(files)} файлов (в пределах лимита {max_files})")
        return 0
    
    # Сортируем по времени модификации (новые первыми)
    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    # Удаляем старые файлы
    deleted_count = 0
    for old_file in files[max_files:]:
        print(f"🗑️  Удаляю: {old_file.name}")
        old_file.unlink()
        deleted_count += 1
    
    print(f"✅ {pattern}: удалено {deleted_count} файлов, осталось {len(files) - deleted_count}")
    return deleted_count

def main():
    print("🗑️  ОЧИСТКА СТАРЫХ ЛОГ-ФАЙЛОВ")
    print("=" * 50)
    print("Оставляю только 10 последних файлов каждого типа")
    print()
    
    logs_dir = Path("logs")
    if not logs_dir.exists():
        print("❌ Директория logs не найдена")
        return
    
    total_deleted = 0
    
    # Основные логи ботов
    print("📄 Основные логи ботов:")
    total_deleted += cleanup_old_files(logs_dir, "bot-sniper-*.log*", 10)
    
    print("\n📄 Audit логи:")
    total_deleted += cleanup_old_files(logs_dir, "audit.log*", 10)
    
    print("\n📄 Логи покупок:")
    total_deleted += cleanup_old_files(logs_dir, "purchases_*.txt*", 10)
    
    print("\n📄 Общие логи:")
    total_deleted += cleanup_old_files(logs_dir, "*.log", 10)
    
    # Архивы
    archive_dir = logs_dir / "archived"
    if archive_dir.exists():
        print("\n📦 Архивы:")
        total_deleted += cleanup_old_files(archive_dir, "*.gz", 10)
    
    print(f"\n✅ Очистка завершена!")
    print(f"📊 Всего удалено файлов: {total_deleted}")
    
    # Показываем текущее состояние
    remaining_files = list(logs_dir.glob("*"))
    remaining_files = [f for f in remaining_files if f.is_file()]
    total_size = sum(f.stat().st_size for f in remaining_files) / (1024 * 1024)
    
    print(f"📊 Текущее состояние:")
    print(f"   - Файлов в logs/: {len(remaining_files)}")
    print(f"   - Общий размер: {total_size:.1f} MB")
    
    if archive_dir.exists():
        archive_files = list(archive_dir.glob("*"))
        archive_files = [f for f in archive_files if f.is_file()]
        print(f"   - Архивов: {len(archive_files)}")

if __name__ == "__main__":
    main()
