#!/usr/bin/env python3
"""
Простой скрипт для проверки баланса SOL в кошельке
"""

import asyncio
import os
import base58
from dotenv import load_dotenv
from solders.keypair import Keypair
from solana.rpc.async_api import AsyncClient

load_dotenv()

async def check_sol_balance():
    """Проверяет баланс SOL в кошельке"""
    
    # Получаем приватный ключ из .env
    private_key_str = os.getenv("SOLANA_PRIVATE_KEY")
    if not private_key_str:
        print("❌ SOLANA_PRIVATE_KEY не найден в .env файле!")
        return
    
    # Создаем кошелек
    try:
        private_key_bytes = base58.b58decode(private_key_str)
        keypair = Keypair.from_bytes(private_key_bytes)
        wallet_address = str(keypair.pubkey())
        print(f"🔑 Адрес кошелька: {wallet_address}")
    except Exception as e:
        print(f"❌ Ошибка создания кошелька: {e}")
        return
    
    # Подключаемся к RPC
    rpc_endpoint = os.getenv("SOLANA_NODE_RPC_ENDPOINT", "https://api.mainnet-beta.solana.com")
    print(f"🌐 RPC endpoint: {rpc_endpoint}")
    
    try:
        async with AsyncClient(rpc_endpoint) as client:
            # Получаем баланс SOL
            balance_response = await client.get_balance(keypair.pubkey())
            
            if balance_response.value is not None:
                balance_lamports = balance_response.value
                balance_sol = balance_lamports / 1_000_000_000  # Конвертируем lamports в SOL
                
                print(f"\n💰 Баланс SOL:")
                print(f"   {balance_sol:.9f} SOL")
                print(f"   {balance_lamports:,} lamports")
                
                # Проверяем достаточность для торговли
                min_recommended = 0.1
                trade_amount = 0.003
                max_trades = int(balance_sol / trade_amount) if balance_sol > 0 else 0
                
                print(f"\n📊 Анализ для торговли:")
                print(f"   Рекомендуемый минимум: {min_recommended} SOL")
                print(f"   Сумма за сделку: {trade_amount} SOL")
                print(f"   Возможно сделок: ~{max_trades}")
                
                if balance_sol >= min_recommended:
                    print("   ✅ Баланс достаточен для торговли")
                elif balance_sol >= trade_amount:
                    print("   ⚠️  Баланс низкий, рекомендуется пополнить")
                else:
                    print("   ❌ Баланс недостаточен для торговли")
                    
            else:
                print("❌ Не удалось получить баланс")
                
    except Exception as e:
        print(f"❌ Ошибка подключения к RPC: {e}")

if __name__ == "__main__":
    asyncio.run(check_sol_balance())
