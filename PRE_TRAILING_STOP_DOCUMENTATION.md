# 🛡️ PRE-TRAILING СТОП-ЛОСС - ДОКУМЕНТАЦИЯ

## 📋 ОПИСАНИЕ

Pre-trailing стоп-лосс - это новая функция защиты прибыли, которая работает **между обычным стоп-лоссом и трейлинг стопом**. Она предотвращает потерю прибыли в случае, когда токен растет, но не достигает уровня активации трейлинга и затем разворачивается вниз.

## 🎯 ПРОБЛЕМА КОТОРУЮ РЕШАЕТ

**Сценарий без pre-trailing стопа:**
```
Покупка → +20% → Разворот → -5% → УБЫТОК
```

**Сценарий с pre-trailing стопом (+12%):**
```
Покупка → +20% → Разворот → +12% → ПРИБЫЛЬ ЗАФИКСИРОВАНА
```

## ⚙️ КОНФИГУРАЦИЯ

### В файле `bots/bot-sniper-2-logs.yaml`:

```yaml
trade:
  # Обычная защита (остается)
  stop_loss_percentage: 0.05        # -5% аварийный стоп
  
  # 🛡️ НОВАЯ защита прибыли
  pre_trailing_stop_loss: 0.12      # +12% стоп до трейлинга
  
  # Трейлинг (остается как есть)
  trailing_stop_activation: 0.25    # +25% активация трейлинга
  trailing_stop_percentage: 0.08    # 8% отступ трейлинга
```

## 🔄 ЛОГИКА РАБОТЫ

### **УРОВНИ ЗАЩИТЫ:**

1. **🚨 АВАРИЙНЫЙ СТОП-ЛОСС: -5%**
   - Если токен сразу упал - защита от больших потерь

2. **🛡️ PRE-TRAILING СТОП: +12%**
   - Если токен рос, но не дошел до +25% и развернулся

3. **🚀 ТРЕЙЛИНГ СТОП: от +25%**
   - Если токен дошел до +25% - включается трейлинг

### **ПРИОРИТЕТЫ ПРОВЕРКИ:**

```python
# 1. Трейлинг стоп (если активирован)
if trailing_activated and price <= trailing_stop_price:
    return TRAILING_STOP

# 2. Аварийный стоп-лосс
if price <= stop_loss_price:
    return STOP_LOSS

# 3. Pre-trailing стоп (если трейлинг НЕ активирован)
if max_profit_reached >= 12% and price <= entry_price * 1.12:
    return PRE_TRAILING_STOP
```

## 📊 ПРИМЕРЫ СЦЕНАРИЕВ

### **Сценарий A - Аварийный стоп:**
```
Покупка 5e-8 → Падение 4.74e-8 (-5.2%) → 🛑 СТОП-ЛОСС
```

### **Сценарий B - Pre-trailing защита:**
```
Покупка 5e-8 → Рост 6e-8 (+20%) → Падение 5.6e-8 (+12%) → 🛡️ PRE-TRAILING СТОП
```

### **Сценарий C - Трейлинг активирован:**
```
Покупка 5e-8 → Рост 6.25e-8 (+25%) → 🚀 ТРЕЙЛИНГ АКТИВИРОВАН
```

### **Сценарий D - Трейлинг срабатывает:**
```
Покупка 5e-8 → +25% → +40% (пик 7e-8) → Падение 6.44e-8 (-8% от пика) → 📉 ТРЕЙЛИНГ СТОП
```

## 🔧 ТЕХНИЧЕСКИЕ ДЕТАЛИ

### **Новые поля в Position:**
```python
pre_trailing_stop_loss: float | None = None  # Процент прибыли для pre-trailing стопа
_max_profit_reached: float = 0.0             # Отслеживание максимальной прибыли
```

### **Новый ExitReason:**
```python
PRE_TRAILING_STOP = "pre_trailing_stop"
```

### **Ключевая логика:**
```python
# Отслеживаем максимальную прибыль
current_profit = (current_price - entry_price) / entry_price
if current_profit > self._max_profit_reached:
    self._max_profit_reached = current_profit

# Проверяем pre-trailing стоп
if (self._max_profit_reached >= self.pre_trailing_stop_loss 
    and current_price <= entry_price * (1 + self.pre_trailing_stop_loss)):
    return True, ExitReason.PRE_TRAILING_STOP
```

## 🎯 ПРЕИМУЩЕСТВА

✅ **Защита прибыли** - не даем токену уйти в минус после роста  
✅ **Умная логика** - стоп работает только после достижения уровня прибыли  
✅ **Двойная защита** - и от больших потерь, и от потери прибыли  
✅ **Гибкость** - можете настроить любой процент в YAML  
✅ **Совместимость** - работает с существующими стратегиями  

## 📈 РЕЗУЛЬТАТ

**Теперь у вас ТРОЙНАЯ защита:**
1. 🚨 Аварийный стоп-лосс (-5%)
2. 🛡️ Pre-trailing стоп (+12%) 
3. 🚀 Трейлинг стоп (от +25%)

**Это значительно снижает риск убыточных сделок!** 🎯
